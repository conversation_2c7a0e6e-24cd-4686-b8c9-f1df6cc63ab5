# 🚀 ENHANCED LANGUAGE MODEL TRAINER - MUCH BETTER VOCABULARY! 🚀
# Now with 50K+ vocabulary like GPT-2 and free data sources!
# Automatically downloads training data from free sources

# GOOGLE COLAB SETUP CELL - RUN THIS FIRST!
"""
# Uncomment and run this cell first in Google Colab:

# Install required packages
!pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
!pip install tqdm matplotlib numpy nltk beautifulsoup4 requests wikipedia-api requests beautifulsoup4 nltk

# Check GPU
import torch
print(f"🔥 CUDA Available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"💾 GPU: {torch.cuda.get_device_name()}")
    print(f"🔥 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
import gc
from tqdm import tqdm
import matplotlib.pyplot as plt
import time
import json
import re
from collections import Counter, defaultdict
import shutil
from datetime import datetime
import requests
from urllib.parse import urljoin, urlparse
import random

# For text processing and data collection
try:
    import nltk
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    from nltk.tokenize import word_tokenize, sent_tokenize
    from nltk.corpus import stopwords
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False
    print("⚠️ NLTK not available - using basic tokenization")

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False
    print("⚠️ BeautifulSoup not available - can't scrape web data")

# Google Colab specific imports
try:
    from google.colab import drive, files
    IN_COLAB = True
    print("🔥 GOOGLE COLAB DETECTED!")
except ImportError:
    IN_COLAB = False
    print("🔥 RUNNING LOCALLY")

def check_environment():
    """Check if environment is properly set up"""
    print("🔍 ENVIRONMENT CHECK:")
    print(f"✅ Python version: {__import__('sys').version}")
    print(f"✅ PyTorch version: {torch.__version__}")
    print(f"✅ CUDA available: {torch.cuda.is_available()}")
    print(f"✅ NLTK available: {NLTK_AVAILABLE}")
    print(f"✅ BeautifulSoup available: {BS4_AVAILABLE}")

    if torch.cuda.is_available():
        print(f"✅ GPU: {torch.cuda.get_device_name()}")
        print(f"✅ GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

        # Test GPU
        try:
            test_tensor = torch.randn(100, 100).cuda()
            result = torch.mm(test_tensor, test_tensor)
            print("✅ GPU test passed!")
            del test_tensor, result
            torch.cuda.empty_cache()
        except Exception as e:
            print(f"❌ GPU test failed: {e}")
    else:
        print("⚠️ No GPU detected - training will be slow")

    print("✅ Environment check complete!")

# Run environment check
check_environment()

# Download NLTK data if needed
if NLTK_AVAILABLE:
    try:
        print("📚 Downloading NLTK data...")
        nltk.download('punkt', quiet=True)
        nltk.download('punkt_tab', quiet=True)
        nltk.download('stopwords', quiet=True)
        print("✅ NLTK data downloaded successfully!")
    except Exception as e:
        print(f"⚠️ NLTK download failed: {e}")
        print("📝 Will use simple tokenization instead")
else:
    print("📝 Using simple tokenization (NLTK not available)")

# Google Drive Integration
def setup_google_drive():
    """Mount Google Drive and create project directory"""
    if not IN_COLAB:
        print("⚠️ Not in Colab - using local directory")
        project_dir = "./enhanced_model_training"
        os.makedirs(project_dir, exist_ok=True)
        return project_dir

    try:
        print("📁 Mounting Google Drive...")
        drive.mount('/content/drive')

        # Create project directory in Drive
        project_dir = "/content/drive/MyDrive/EnhancedModelTraining"
        os.makedirs(project_dir, exist_ok=True)

        # Create subdirectories
        subdirs = ['models', 'tokenizers', 'checkpoints', 'logs', 'plots', 'data']
        for subdir in subdirs:
            os.makedirs(f"{project_dir}/{subdir}", exist_ok=True)

        print(f"✅ Google Drive mounted! Project directory: {project_dir}")
        return project_dir

    except Exception as e:
        print(f"❌ Failed to mount Google Drive: {e}")
        print("📁 Using local directory instead")
        project_dir = "./enhanced_model_training"
        os.makedirs(project_dir, exist_ok=True)
        return project_dir

# Initialize project directory
PROJECT_DIR = setup_google_drive()

# 🌐 FREE DATA COLLECTION FROM VARIOUS SOURCES
class DataCollector:
    def __init__(self, project_dir=None):
        self.project_dir = project_dir or PROJECT_DIR
        self.data_dir = f"{self.project_dir}/data"
        os.makedirs(self.data_dir, exist_ok=True)
        self.collected_texts = []
    
    def get_gutenberg_books(self, book_ids=None, max_books=10):
        """Download free books from Project Gutenberg"""
        print("📚 Downloading books from Project Gutenberg...")
        
        if book_ids is None:
            # Popular public domain books
            book_ids = [
                1342,  # Pride and Prejudice
                11,    # Alice's Adventures in Wonderland
                74,    # Adventures of Tom Sawyer
                84,    # Frankenstein
                1661,  # Sherlock Holmes
                2701,  # Moby Dick
                16,    # Peter Pan
                1400,  # Great Expectations
                5200,  # Metamorphosis
                844,   # The Importance of Being Earnest
                98,    # A Tale of Two Cities
                1184,  # The Count of Monte Cristo
                215,   # The Call of the Wild
                43,    # Dr. Jekyll and Mr. Hyde
                345    # Dracula
            ]
        
        book_ids = book_ids[:max_books]  # Limit number of books
        texts = []
        
        for book_id in tqdm(book_ids, desc="Downloading books"):
            try:
                url = f"https://www.gutenberg.org/files/{book_id}/{book_id}-0.txt"
                response = requests.get(url, timeout=30)
                
                if response.status_code == 200:
                    text = response.text
                    
                    # Clean Gutenberg text (remove headers/footers)
                    lines = text.split('\n')
                    start_idx = 0
                    end_idx = len(lines)
                    
                    # Find start of actual content
                    for i, line in enumerate(lines):
                        if "*** START OF" in line.upper() or "***START OF" in line.upper():
                            start_idx = i + 1
                            break
                    
                    # Find end of actual content
                    for i in range(len(lines) - 1, 0, -1):
                        if "*** END OF" in lines[i].upper() or "***END OF" in lines[i].upper():
                            end_idx = i
                            break
                    
                    clean_text = '\n'.join(lines[start_idx:end_idx])
                    
                    # Basic cleaning
                    clean_text = re.sub(r'\n\n+', '\n\n', clean_text)
                    clean_text = clean_text.strip()
                    
                    if len(clean_text) > 1000:  # Only keep substantial texts
                        texts.append(clean_text)
                        print(f"✅ Downloaded book {book_id} ({len(clean_text):,} chars)")
                    
                time.sleep(0.5)  # Be respectful to the server
                
            except Exception as e:
                print(f"❌ Failed to download book {book_id}: {e}")
        
        print(f"📖 Successfully downloaded {len(texts)} books from Gutenberg")
        return texts
    
    def get_wikipedia_articles(self, topics=None, max_articles=20):
        """Get Wikipedia articles on various topics"""
        print("🌐 Downloading Wikipedia articles...")
        
        if topics is None:
            topics = [
                "Machine learning", "Artificial intelligence", "Computer science",
                "Physics", "Chemistry", "Biology", "Mathematics", "History",
                "Literature", "Philosophy", "Psychology", "Economics",
                "Geography", "Astronomy", "Technology", "Science",
                "Art", "Music", "Film", "Sports"
            ]
        
        topics = topics[:max_articles]
        texts = []
        
        for topic in tqdm(topics, desc="Downloading Wikipedia"):
            try:
                # Use Wikipedia API
                api_url = "https://en.wikipedia.org/api/rest_v1/page/summary/" + topic.replace(" ", "_")
                response = requests.get(api_url, timeout=15)
                
                if response.status_code == 200:
                    data = response.json()
                    if 'extract' in data:
                        extract = data['extract']
                        
                        # Get full article content
                        content_url = f"https://en.wikipedia.org/api/rest_v1/page/mobile-sections/{topic.replace(' ', '_')}"
                        content_response = requests.get(content_url, timeout=15)
                        
                        if content_response.status_code == 200:
                            content_data = content_response.json()
                            full_text = extract + "\n\n"
                            
                            # Extract text from sections
                            if 'sections' in content_data:
                                for section in content_data['sections'][:10]:  # First 10 sections
                                    if 'text' in section:
                                        # Remove HTML tags
                                        if BS4_AVAILABLE:
                                            soup = BeautifulSoup(section['text'], 'html.parser')
                                            section_text = soup.get_text()
                                        else:
                                            section_text = re.sub(r'<[^>]+>', '', section['text'])
                                        
                                        full_text += section_text + "\n\n"
                            
                            # Clean text
                            full_text = re.sub(r'\n\n+', '\n\n', full_text)
                            full_text = full_text.strip()
                            
                            if len(full_text) > 500:
                                texts.append(full_text)
                                print(f"✅ Downloaded {topic} ({len(full_text):,} chars)")
                
                time.sleep(0.5)  # Be respectful
                
            except Exception as e:
                print(f"❌ Failed to download {topic}: {e}")
        
        print(f"🌐 Successfully downloaded {len(texts)} Wikipedia articles")
        return texts
    
    def get_news_articles(self, max_articles=10):
        """Get news articles from RSS feeds with fallback content"""
        print("📰 Downloading news articles...")

        # Try RSS feeds first (with shorter timeout for Colab)
        rss_feeds = [
            "http://rss.cnn.com/rss/edition.rss",
            "https://feeds.bbci.co.uk/news/world/rss.xml",
        ]

        texts = []
        articles_per_feed = max_articles // len(rss_feeds)

        for feed_url in rss_feeds:
            try:
                response = requests.get(feed_url, timeout=5)  # Shorter timeout
                if response.status_code == 200 and BS4_AVAILABLE:
                    soup = BeautifulSoup(response.content, 'xml')
                    items = soup.find_all('item')[:articles_per_feed]

                    for item in items:
                        try:
                            title = item.find('title')
                            description = item.find('description')

                            if title and description:
                                article_text = f"{title.get_text()}\n\n{description.get_text()}"
                                article_text = re.sub(r'<[^>]+>', '', article_text)

                                if len(article_text) > 100:
                                    texts.append(article_text)
                        except:
                            continue

                time.sleep(0.2)  # Shorter delay

            except Exception as e:
                print(f"⚠️ Skipping {feed_url}: Network issue")

        # Add fallback news-style content if we didn't get enough
        if len(texts) < max_articles // 2:
            print("📰 Adding fallback news content...")
            fallback_news = [
                """Breaking: Scientists Develop New AI Model

                Researchers at leading universities have announced a breakthrough in artificial intelligence technology. The new model demonstrates improved performance across multiple benchmarks and shows promise for real-world applications. The development represents a significant step forward in machine learning capabilities.""",

                """Technology Update: Quantum Computing Advances

                Major technology companies are making significant investments in quantum computing research. Recent developments have shown promising results in solving complex computational problems that were previously intractable. Industry experts predict widespread adoption within the next decade.""",

                """Climate Research: New Findings Published

                Environmental scientists have published new research on climate change patterns and their global impact. The study provides valuable insights into long-term environmental trends and offers recommendations for sustainable development practices. The findings have important implications for policy makers worldwide.""",

                """Economic Analysis: Market Trends Report

                Financial analysts report significant changes in global market conditions. The latest economic indicators suggest shifting patterns in international trade and investment. Experts recommend careful monitoring of emerging trends and adaptive strategies for businesses and investors.""",

                """Health News: Medical Research Breakthrough

                Medical researchers announce promising developments in treatment methodologies. The new approach shows potential for improving patient outcomes and reducing treatment costs. Clinical trials are expected to begin soon, with results anticipated within the next two years."""
            ]

            texts.extend(fallback_news[:max_articles - len(texts)])

        print(f"📰 Successfully collected {len(texts)} news articles")
        return texts
    
    def collect_all_data(self, books=5, wiki_articles=15, news_articles=10):
        """Collect data from all sources"""
        print("🌍 COLLECTING TRAINING DATA FROM FREE SOURCES!")
        print("=" * 60)
        
        all_texts = []
        
        # Project Gutenberg books
        if books > 0:
            gutenberg_texts = self.get_gutenberg_books(max_books=books)
            all_texts.extend(gutenberg_texts)
        
        # Wikipedia articles
        if wiki_articles > 0:
            wiki_texts = self.get_wikipedia_articles(max_articles=wiki_articles)
            all_texts.extend(wiki_texts)
        
        # News articles
        if news_articles > 0:
            news_texts = self.get_news_articles(max_articles=news_articles)
            all_texts.extend(news_texts)

        # Add comprehensive fallback content if we don't have enough data
        if len(all_texts) < 5:
            print("📚 Adding comprehensive fallback training content...")
            fallback_content = [
                """
                Artificial intelligence represents one of the most significant technological advances of our time.
                Machine learning algorithms can process vast amounts of data to identify patterns and make predictions.
                Deep learning networks use multiple layers of artificial neurons to learn complex representations.
                Natural language processing enables computers to understand and generate human language.
                Computer vision allows machines to interpret and analyze visual information from the world around us.

                The field of AI encompasses many different approaches and techniques. Supervised learning uses labeled
                examples to train models that can make predictions on new data. Unsupervised learning finds hidden
                patterns in data without explicit labels. Reinforcement learning trains agents to make decisions
                through trial and error, receiving rewards for good actions and penalties for poor ones.
                """,

                """
                The history of computing spans several decades of remarkable innovation and progress.
                Early computers were room-sized machines that could perform basic calculations.
                The development of transistors revolutionized electronics and made modern computers possible.
                Personal computers brought computing power to homes and offices around the world.
                The internet connected billions of devices and transformed how we communicate and share information.

                Modern computing continues to evolve at a rapid pace. Cloud computing provides scalable resources
                on demand. Mobile devices put powerful computers in everyone's pocket. Quantum computing promises
                to solve certain problems exponentially faster than classical computers. Edge computing brings
                processing power closer to where data is generated, reducing latency and improving efficiency.
                """,

                """
                Scientific research drives innovation and expands our understanding of the natural world.
                Researchers use the scientific method to test hypotheses and validate theories.
                Peer review ensures that published research meets high standards of quality and rigor.
                Collaboration between scientists accelerates discovery and promotes knowledge sharing.
                Technology enables new forms of research and opens up previously impossible investigations.

                Different scientific disciplines employ various methodologies and tools. Physics explores the
                fundamental laws governing matter and energy. Chemistry studies the composition and behavior of
                substances. Biology investigates living organisms and their interactions. Mathematics provides
                the language and tools for describing and analyzing complex systems across all sciences.
                """,

                """
                Education plays a crucial role in personal development and societal progress. Learning is a
                lifelong process that begins in early childhood and continues throughout adulthood. Effective
                teaching methods adapt to different learning styles and individual needs. Technology has
                transformed education, providing new tools for instruction and assessment.

                Modern educational systems face many challenges and opportunities. Online learning platforms
                make education more accessible to people around the world. Personalized learning adapts to
                individual student needs and preferences. Collaborative learning encourages students to work
                together and learn from each other. Assessment methods are evolving to better measure student
                understanding and progress.
                """,

                """
                Communication is fundamental to human society and culture. Language allows us to share ideas,
                emotions, and experiences with others. Written communication preserves knowledge across time
                and space. Digital communication technologies have revolutionized how we connect with people
                around the world.

                Effective communication requires clarity, empathy, and understanding. Active listening helps
                us better understand others' perspectives. Nonverbal communication conveys important information
                through body language and facial expressions. Cultural differences influence communication styles
                and expectations. Technology continues to create new forms of communication and interaction.
                """
            ]
            all_texts.extend(fallback_content)

        print(f"\n📊 DATA COLLECTION SUMMARY:")
        print(f"✅ Total texts collected: {len(all_texts)}")
        
        total_chars = sum(len(text) for text in all_texts)
        total_words = sum(len(text.split()) for text in all_texts)
        
        print(f"✅ Total characters: {total_chars:,}")
        print(f"✅ Total words (estimated): {total_words:,}")
        print(f"✅ Average text length: {total_chars // len(all_texts) if all_texts else 0:,} chars")
        
        # Save collected data
        data_file = f"{self.data_dir}/collected_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(all_texts, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Data saved to: {data_file}")
        
        self.collected_texts = all_texts
        return all_texts

# 🛠️ ENHANCED TOKENIZER WITH SUBWORD SUPPORT
class EnhancedTokenizer:
    def __init__(self, vocab_size=50000):
        self.word_to_id = {}
        self.id_to_word = {}
        self.vocab_size = 0
        self.target_vocab_size = vocab_size
        
        # Special tokens
        self.special_tokens = {
            '<PAD>': 0,
            '<UNK>': 1,
            '<SOS>': 2,
            '<EOS>': 3,
            '<MASK>': 4,
        }
        
        for token, idx in self.special_tokens.items():
            self.word_to_id[token] = idx
            self.id_to_word[idx] = token
        
        self.vocab_size = len(self.special_tokens)
        
        # For subword tokenization
        self.subword_dict = {}
        print(f"🔧 Enhanced tokenizer initialized (target vocab: {vocab_size:,})")
    
    def advanced_tokenize(self, text):
        """Advanced tokenization with better handling"""
        if NLTK_AVAILABLE:
            # Use NLTK for better tokenization
            tokens = word_tokenize(text.lower())
        else:
            # Fallback to regex-based tokenization
            text = text.lower()
            # Handle contractions
            text = re.sub(r"won't", "will not", text)
            text = re.sub(r"can't", "cannot", text)
            text = re.sub(r"n't", " not", text)
            text = re.sub(r"'re", " are", text)
            text = re.sub(r"'ve", " have", text)
            text = re.sub(r"'ll", " will", text)
            text = re.sub(r"'d", " would", text)
            text = re.sub(r"'m", " am", text)
            
            # Split on punctuation and whitespace
            tokens = re.findall(r"\b\w+\b|[^\w\s]", text)
        
        return [token for token in tokens if token.strip()]
    
    def build_subword_vocab(self, word_counts, max_subwords=10000):
        """Build subword vocabulary for better coverage"""
        print("🔤 Building subword vocabulary...")
        
        # Get common subwords (2-4 characters)
        subword_counts = Counter()
        
        for word, count in tqdm(word_counts.items(), desc="Extracting subwords"):
            if len(word) > 3:  # Only for longer words
                for i in range(len(word) - 1):
                    for j in range(i + 2, min(i + 5, len(word) + 1)):
                        subword = word[i:j]
                        if len(subword) >= 2:
                            subword_counts[f"##{subword}"] += count
        
        # Add most common subwords
        common_subwords = subword_counts.most_common(max_subwords)
        for subword, count in common_subwords:
            if self.vocab_size < self.target_vocab_size and count > 5:
                self.word_to_id[subword] = self.vocab_size
                self.id_to_word[self.vocab_size] = subword
                self.vocab_size += 1
                self.subword_dict[subword] = count
        
        print(f"✅ Added {len(self.subword_dict)} subwords to vocabulary")
    
    def build_vocab(self, texts, min_freq=2):
        """Build enhanced vocabulary with better coverage"""
        print("📚 Building enhanced vocabulary from texts...")
        
        # Count word frequencies
        word_counts = Counter()
        total_words = 0
        
        for text in tqdm(texts, desc="Tokenizing texts"):
            words = self.advanced_tokenize(text)
            word_counts.update(words)
            total_words += len(words)
        
        print(f"📊 Found {len(word_counts):,} unique tokens, {total_words:,} total tokens")
        
        # Build subword vocabulary first
        remaining_vocab = self.target_vocab_size - self.vocab_size
        self.build_subword_vocab(word_counts, max_subwords=remaining_vocab // 3)
        
        # Add most frequent words
        remaining_vocab = self.target_vocab_size - self.vocab_size
        word_list = word_counts.most_common()
        
        # Filter by frequency and add to vocab
        added_words = 0
        for word, count in word_list:
            if count >= min_freq and word not in self.word_to_id and self.vocab_size < self.target_vocab_size:
                self.word_to_id[word] = self.vocab_size
                self.id_to_word[self.vocab_size] = word
                self.vocab_size += 1
                added_words += 1
        
        print(f"🎯 Final vocabulary size: {self.vocab_size:,} tokens")
        print(f"📈 Added {added_words:,} words")
        print(f"📈 Coverage: {len([w for w, c in word_counts.items() if c >= min_freq]):,} words above min_freq")
        
        # Calculate coverage
        covered_tokens = sum(count for word, count in word_counts.items() if word in self.word_to_id)
        coverage_percent = (covered_tokens / total_words) * 100
        print(f"🎯 Vocabulary coverage: {coverage_percent:.1f}%")
        
        # Show sample vocabulary
        print("\n🔤 Sample vocabulary:")
        regular_words = [(w, i) for w, i in self.word_to_id.items() if not w.startswith('##') and w not in self.special_tokens]
        for i, (word, idx) in enumerate(sorted(regular_words, key=lambda x: x[1])[:20]):
            count = word_counts.get(word, 0)
            print(f"  {idx:5d}: '{word}' (count: {count})")
        
        if self.subword_dict:
            print("\n🧩 Sample subwords:")
            for i, (subword, count) in enumerate(list(self.subword_dict.items())[:10]):
                print(f"  {self.word_to_id[subword]:5d}: '{subword}' (count: {count})")
        
        return self.vocab_size
    
    def encode_with_subwords(self, text):
        """Encode text using subwords for unknown words"""
        words = self.advanced_tokenize(text)
        token_ids = []
        
        for word in words:
            if word in self.word_to_id:
                token_ids.append(self.word_to_id[word])
            else:
                # Try to break down using subwords
                subword_tokens = []
                remaining = word
                
                while remaining and len(subword_tokens) < 5:  # Max 5 subwords per word
                    found_subword = False
                    
                    # Try longest subword first
                    for length in range(min(4, len(remaining)), 1, -1):
                        if length <= len(remaining):
                            candidate = f"##{remaining[:length]}"
                            if candidate in self.word_to_id:
                                subword_tokens.append(self.word_to_id[candidate])
                                remaining = remaining[length:]
                                found_subword = True
                                break
                    
                    if not found_subword:
                        remaining = remaining[1:]  # Skip one character
                
                if subword_tokens:
                    token_ids.extend(subword_tokens)
                else:
                    token_ids.append(self.special_tokens['<UNK>'])
        
        return token_ids
    
    def encode(self, text):
        """Main encoding function"""
        return self.encode_with_subwords(text)
    
    def decode(self, token_ids):
        """Enhanced decoding with subword handling"""
        words = []
        current_word = ""
        
        for token_id in token_ids:
            if token_id in self.id_to_word:
                token = self.id_to_word[token_id]
                
                if token in ['<PAD>', '<SOS>', '<EOS>', '<MASK>']:
                    continue
                elif token == '<UNK>':
                    words.append('<UNK>')
                elif token.startswith('##'):
                    # Subword token
                    current_word += token[2:]  # Remove ##
                else:
                    # Regular word
                    if current_word:
                        words.append(current_word)
                        current_word = ""
                    words.append(token)
        
        # Add any remaining partial word
        if current_word:
            words.append(current_word)
        
        # Join words and fix punctuation
        text = ' '.join(words)
        text = re.sub(r' ([.,!?;:])', r'\1', text)
        text = re.sub(r' \'', r'\'', text)
        return text
    
    def save(self, filename=None):
        """Save enhanced tokenizer"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"enhanced_tokenizer_{timestamp}.json"

        path = f"{PROJECT_DIR}/tokenizers/{filename}"

        vocab_data = {
            'word_to_id': self.word_to_id,
            'id_to_word': {str(k): v for k, v in self.id_to_word.items()},
            'vocab_size': self.vocab_size,
            'target_vocab_size': self.target_vocab_size,
            'special_tokens': self.special_tokens,
            'subword_dict': self.subword_dict,
            'created_at': datetime.now().isoformat()
        }

        with open(path, 'w', encoding='utf-8') as f:
            json.dump(vocab_data, f, indent=2, ensure_ascii=False)

        backup_path = f"{PROJECT_DIR}/tokenizers/latest_enhanced_tokenizer.json"
        shutil.copy2(path, backup_path)

        print(f"💾 Enhanced tokenizer saved to {path}")
        return path

    def load(self, path):
        """Load enhanced tokenizer"""
        possible_paths = [
            path,
            f"{PROJECT_DIR}/tokenizers/{path}",
            f"{PROJECT_DIR}/tokenizers/latest_enhanced_tokenizer.json"
        ]

        for try_path in possible_paths:
            if os.path.exists(try_path):
                with open(try_path, 'r', encoding='utf-8') as f:
                    vocab_data = json.load(f)

                self.word_to_id = vocab_data['word_to_id']
                self.id_to_word = {int(k): v for k, v in vocab_data['id_to_word'].items()}
                self.vocab_size = vocab_data['vocab_size']
                self.target_vocab_size = vocab_data.get('target_vocab_size', 50000)
                self.special_tokens = vocab_data['special_tokens']
                self.subword_dict = vocab_data.get('subword_dict', {})
                
                print(f"📁 Enhanced tokenizer loaded from {try_path}")
                print(f"📊 Vocabulary size: {self.vocab_size:,}")
                return try_path

        raise FileNotFoundError(f"Could not find tokenizer at any of: {possible_paths}")

# Copy the rest of the model architecture (same as before)
class CustomLanguageModel(nn.Module):
    def __init__(self, vocab_size, dim=512, n_layers=8, n_heads=8, max_seq_len=512):
        super().__init__()
        self.vocab_size = vocab_size
        self.dim = dim
        self.max_seq_len = max_seq_len
        
        # Embeddings
        self.tok_emb = nn.Embedding(vocab_size, dim)
        self.pos_emb = nn.Embedding(max_seq_len, dim)
        self.dropout = nn.Dropout(0.1)
        
        # Transformer blocks
        self.blocks = nn.ModuleList([
            TransformerBlock(dim, n_heads) for _ in range(n_layers)
        ])
        
        self.ln_f = nn.LayerNorm(dim)
        self.head = nn.Linear(dim, vocab_size)
        
        # Initialize weights
        self.apply(self._init_weights)
        
        print(f"🤖 Enhanced model created: {self.count_params():,} parameters")
        print(f"📐 Architecture: {n_layers} layers, {n_heads} heads, {dim} dim")
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
    
    def count_params(self):
        return sum(p.numel() for p in self.parameters())
    
    def forward(self, x, targets=None):
        B, T = x.shape
        
        # Get embeddings
        tok_emb = self.tok_emb(x)
        pos_emb = self.pos_emb(torch.arange(T, device=x.device))
        x = self.dropout(tok_emb + pos_emb)
        
        # Apply transformer blocks
        for block in self.blocks:
            x = block(x)
        
        x = self.ln_f(x)
        logits = self.head(x)
        
        loss = None
        if targets is not None:
            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=0)
        
        return logits, loss

class TransformerBlock(nn.Module):
    def __init__(self, dim, n_heads):
        super().__init__()
        self.attn = MultiHeadAttention(dim, n_heads)
        self.mlp = nn.Sequential(
            nn.Linear(dim, dim * 4),
            nn.GELU(),
            nn.Linear(dim * 4, dim),
            nn.Dropout(0.1)
        )
        self.ln1 = nn.LayerNorm(dim)
        self.ln2 = nn.LayerNorm(dim)
        self.dropout1 = nn.Dropout(0.1)
        self.dropout2 = nn.Dropout(0.1)
    
    def forward(self, x):
        x = x + self.dropout1(self.attn(self.ln1(x)))
        x = x + self.dropout2(self.mlp(self.ln2(x)))
        return x

# Continuing from MultiHeadAttention class
class MultiHeadAttention(nn.Module):
    def __init__(self, dim, n_heads):
        super().__init__()
        assert dim % n_heads == 0
        
        self.n_heads = n_heads
        self.dim = dim
        self.head_dim = dim // n_heads
        
        self.qkv = nn.Linear(dim, dim * 3, bias=False)
        self.proj = nn.Linear(dim, dim)
        self.dropout = nn.Dropout(0.1)
    
    def forward(self, x):
        B, T, C = x.shape
        
        # Get Q, K, V
        qkv = self.qkv(x)
        q, k, v = qkv.chunk(3, dim=-1)
        
        # Reshape for multi-head attention
        q = q.view(B, T, self.n_heads, self.head_dim).transpose(1, 2)
        k = k.view(B, T, self.n_heads, self.head_dim).transpose(1, 2)
        v = v.view(B, T, self.n_heads, self.head_dim).transpose(1, 2)
        
        # Scaled dot-product attention
        att = (q @ k.transpose(-2, -1)) * (1.0 / np.sqrt(k.size(-1)))
        
        # Causal mask
        mask = torch.tril(torch.ones(T, T, device=x.device)).view(1, 1, T, T)
        att = att.masked_fill(mask == 0, float('-inf'))
        
        att = F.softmax(att, dim=-1)
        att = self.dropout(att)
        
        # Apply attention to values
        out = att @ v
        out = out.transpose(1, 2).contiguous().view(B, T, C)
        
        return self.proj(out)

# 📊 ENHANCED DATASET CLASS
class EnhancedTextDataset(Dataset):
    def __init__(self, texts, tokenizer, seq_len=256, stride=128):
        self.tokenizer = tokenizer
        self.seq_len = seq_len
        self.examples = []
        
        print("🔄 Processing texts into training examples...")
        total_chars = 0
        skipped_short = 0
        
        for text in tqdm(texts, desc="Creating examples"):
            if len(text.strip()) < 100:  # Skip very short texts
                skipped_short += 1
                continue
                
            total_chars += len(text)
            
            # Encode text
            tokens = tokenizer.encode(text)
            
            # Create overlapping sequences
            for i in range(0, len(tokens) - seq_len, stride):
                input_ids = tokens[i:i + seq_len]
                target_ids = tokens[i + 1:i + seq_len + 1]
                
                if len(input_ids) == seq_len and len(target_ids) == seq_len:
                    self.examples.append({
                        'input_ids': torch.tensor(input_ids, dtype=torch.long),
                        'target_ids': torch.tensor(target_ids, dtype=torch.long)
                    })
        
        print(f"📊 Dataset created:")
        print(f"  • Total examples: {len(self.examples):,}")
        print(f"  • Total characters processed: {total_chars:,}")
        print(f"  • Skipped short texts: {skipped_short}")
        print(f"  • Sequence length: {seq_len}")
        print(f"  • Stride: {stride}")
    
    def __len__(self):
        return len(self.examples)
    
    def __getitem__(self, idx):
        return self.examples[idx]

# 🎯 ENHANCED TRAINER CLASS
class EnhancedModelTrainer:
    def __init__(self, model, tokenizer, project_dir=None):
        self.model = model
        self.tokenizer = tokenizer
        self.project_dir = project_dir or PROJECT_DIR
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        self.model.to(self.device)
        
        # Training history
        self.train_losses = []
        self.val_losses = []
        self.learning_rates = []
        
        print(f"🚀 Enhanced trainer initialized")
        print(f"📱 Device: {self.device}")
        if torch.cuda.is_available():
            print(f"🔥 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    def train_step(self, batch, optimizer, scaler=None):
        """Single training step with mixed precision support"""
        self.model.train()
        
        input_ids = batch['input_ids'].to(self.device)
        target_ids = batch['target_ids'].to(self.device)
        
        if scaler is not None:
            # Mixed precision training
            with torch.autocast(device_type='cuda' if torch.cuda.is_available() else 'cpu'):
                logits, loss = self.model(input_ids, target_ids)
            
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
            optimizer.zero_grad()
        else:
            # Regular training
            logits, loss = self.model(input_ids, target_ids)
            loss.backward()
            optimizer.step()
            optimizer.zero_grad()
        
        return loss.item()
    
    def validate(self, val_loader):
        """Validation step"""
        self.model.eval()
        total_loss = 0
        num_batches = 0
        
        with torch.no_grad():
            for batch in val_loader:
                input_ids = batch['input_ids'].to(self.device)
                target_ids = batch['target_ids'].to(self.device)
                
                with torch.autocast(device_type='cuda' if torch.cuda.is_available() else 'cpu'):
                    logits, loss = self.model(input_ids, target_ids)
                
                total_loss += loss.item()
                num_batches += 1
        
        return total_loss / num_batches if num_batches > 0 else 0
    
    def train(self, train_loader, val_loader=None, epochs=10, lr=3e-4, 
              warmup_steps=1000, save_every=2, generate_every=5):
        """Enhanced training loop with advanced features"""
        
        print("🚀 STARTING ENHANCED TRAINING!")
        print("=" * 60)
        
        # Setup optimizer and scheduler
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=lr, weight_decay=0.01)
        
        # Learning rate scheduler with warmup
        total_steps = len(train_loader) * epochs
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer, max_lr=lr, total_steps=total_steps,
            pct_start=warmup_steps/total_steps, anneal_strategy='cos'
        )
        
        # Mixed precision training
        scaler = torch.cuda.amp.GradScaler() if torch.cuda.is_available() else None
        
        # Training metrics
        best_val_loss = float('inf')
        steps = 0
        
        for epoch in range(epochs):
            epoch_start_time = time.time()
            epoch_loss = 0
            num_batches = 0
            
            # Training loop
            progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}")
            
            for batch_idx, batch in enumerate(progress_bar):
                # Training step
                loss = self.train_step(batch, optimizer, scaler)
                scheduler.step()
                
                epoch_loss += loss
                num_batches += 1
                steps += 1
                
                # Update progress bar
                current_lr = scheduler.get_last_lr()[0]
                progress_bar.set_postfix({
                    'loss': f'{loss:.4f}',
                    'lr': f'{current_lr:.6f}',
                    'step': steps
                })
                
                # Clear cache periodically
                if steps % 100 == 0 and torch.cuda.is_available():
                    torch.cuda.empty_cache()
            
            # Calculate epoch metrics
            avg_train_loss = epoch_loss / num_batches
            epoch_time = time.time() - epoch_start_time
            current_lr = scheduler.get_last_lr()[0]
            
            # Validation
            val_loss = 0
            if val_loader is not None:
                val_loss = self.validate(val_loader)
                self.val_losses.append(val_loss)
            
            # Save metrics
            self.train_losses.append(avg_train_loss)
            self.learning_rates.append(current_lr)
            
            # Print epoch summary
            print(f"\n📊 EPOCH {epoch+1} SUMMARY:")
            print(f"  ⏱️  Time: {epoch_time:.1f}s")
            print(f"  📉 Train Loss: {avg_train_loss:.4f}")
            if val_loader is not None:
                print(f"  📊 Val Loss: {val_loss:.4f}")
            print(f"  📈 Learning Rate: {current_lr:.6f}")
            print(f"  🔥 GPU Memory: {torch.cuda.memory_allocated()/1e9:.1f}GB" if torch.cuda.is_available() else "")
            
            # Save checkpoint
            if (epoch + 1) % save_every == 0:
                self.save_checkpoint(epoch + 1, avg_train_loss, val_loss)
            
            # Generate sample text
            if (epoch + 1) % generate_every == 0:
                self.generate_sample("The future of artificial intelligence", max_length=100)
            
            # Early stopping check
            if val_loader is not None and val_loss < best_val_loss:
                best_val_loss = val_loss
                self.save_model(f"best_model_epoch_{epoch+1}.pt")
            
            print("-" * 60)
        
        print("🎉 TRAINING COMPLETED!")
        self.save_model("final_enhanced_model.pt")
        self.plot_training_history()
        
        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'learning_rates': self.learning_rates
        }
    
    def save_checkpoint(self, epoch, train_loss, val_loss=None):
        """Save training checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'learning_rates': self.learning_rates,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'vocab_size': self.tokenizer.vocab_size,
            'model_config': {
                'vocab_size': self.model.vocab_size,
                'dim': self.model.dim,
                'max_seq_len': self.model.max_seq_len
            }
        }
        
        checkpoint_path = f"{self.project_dir}/checkpoints/checkpoint_epoch_{epoch}.pt"
        torch.save(checkpoint, checkpoint_path)
        print(f"💾 Checkpoint saved: {checkpoint_path}")
    
    def save_model(self, filename):
        """Save complete model"""
        model_path = f"{self.project_dir}/models/{filename}"
        
        save_dict = {
            'model_state_dict': self.model.state_dict(),
            'model_config': {
                'vocab_size': self.model.vocab_size,
                'dim': self.model.dim,
                'max_seq_len': self.model.max_seq_len
            },
            'tokenizer_vocab_size': self.tokenizer.vocab_size,
            'training_history': {
                'train_losses': self.train_losses,
                'val_losses': self.val_losses,
                'learning_rates': self.learning_rates
            },
            'timestamp': datetime.now().isoformat()
        }
        
        torch.save(save_dict, model_path)
        print(f"🎯 Model saved: {model_path}")
        return model_path
    
    def plot_training_history(self):
        """Plot training curves"""
        if not self.train_losses:
            return
        
        plt.figure(figsize=(15, 5))
        
        # Loss plot
        plt.subplot(1, 3, 1)
        plt.plot(self.train_losses, label='Train Loss', color='blue')
        if self.val_losses:
            plt.plot(self.val_losses, label='Val Loss', color='red')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Training Loss')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Learning rate plot
        plt.subplot(1, 3, 2)
        plt.plot(self.learning_rates, color='green')
        plt.xlabel('Epoch')
        plt.ylabel('Learning Rate')
        plt.title('Learning Rate Schedule')
        plt.grid(True, alpha=0.3)
        
        # Loss improvement plot
        plt.subplot(1, 3, 3)
        if len(self.train_losses) > 1:
            improvements = [(self.train_losses[i-1] - self.train_losses[i]) / self.train_losses[i-1] * 100 
                          for i in range(1, len(self.train_losses))]
            plt.plot(improvements, color='purple')
            plt.xlabel('Epoch')
            plt.ylabel('Loss Improvement (%)')
            plt.title('Training Progress')
            plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plot_path = f"{self.project_dir}/plots/training_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"📈 Training plots saved to: {plot_path}")
    
    @torch.no_grad()
    def generate_sample(self, prompt="The", max_length=100, temperature=0.8, top_k=50):
        """Generate sample text"""
        self.model.eval()
        
        print(f"\n🎭 GENERATING SAMPLE TEXT:")
        print(f"📝 Prompt: '{prompt}'")
        
        # Encode prompt
        tokens = self.tokenizer.encode(prompt)
        input_ids = torch.tensor([tokens], dtype=torch.long, device=self.device)
        
        generated_tokens = tokens.copy()
        
        for _ in range(max_length):
            if input_ids.size(1) >= self.model.max_seq_len:
                input_ids = input_ids[:, -self.model.max_seq_len+1:]
            
            with torch.autocast(device_type='cuda' if torch.cuda.is_available() else 'cpu'):
                logits, _ = self.model(input_ids)
            
            # Apply temperature and top-k sampling
            logits = logits[:, -1, :] / temperature
            
            if top_k > 0:
                v, _ = torch.topk(logits, min(top_k, logits.size(-1)))
                logits[logits < v[:, [-1]]] = -float('inf')
            
            probs = F.softmax(logits, dim=-1)
            next_token = torch.multinomial(probs, num_samples=1)
            
            generated_tokens.append(next_token.item())
            input_ids = torch.cat([input_ids, next_token], dim=1)
            
            # Stop at end token
            if next_token.item() == self.tokenizer.special_tokens.get('<EOS>', -1):
                break
        
        generated_text = self.tokenizer.decode(generated_tokens)
        print(f"🎯 Generated: '{generated_text}'")
        print("-" * 60)
        
        return generated_text

# 🚀 MAIN EXECUTION PIPELINE
def main_training_pipeline():
    """Complete training pipeline with data collection"""
    
    print("🌟 ENHANCED LANGUAGE MODEL TRAINING PIPELINE")
    print("=" * 70)
    
    # 1. Collect training data
    print("\n📊 STEP 1: DATA COLLECTION")
    collector = DataCollector()
    texts = collector.collect_all_data(books=8, wiki_articles=20, news_articles=15)
    
    if not texts:
        print("❌ No training data collected!")
        return None
    
    # 2. Build enhanced tokenizer
    print("\n🔧 STEP 2: BUILDING ENHANCED TOKENIZER")
    tokenizer = EnhancedTokenizer(vocab_size=50000)
    vocab_size = tokenizer.build_vocab(texts, min_freq=3)
    tokenizer.save()
    
    # 3. Create datasets
    print("\n📚 STEP 3: CREATING DATASETS")
    
    # Split data into train/val
    split_idx = int(0.9 * len(texts))
    train_texts = texts[:split_idx]
    val_texts = texts[split_idx:] if len(texts) > 10 else texts[-2:]  # Ensure we have val data
    
    train_dataset = EnhancedTextDataset(train_texts, tokenizer, seq_len=256, stride=128)
    val_dataset = EnhancedTextDataset(val_texts, tokenizer, seq_len=256, stride=256)
    
    # Create data loaders
    batch_size = 16 if torch.cuda.is_available() else 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2)
    
    print(f"📊 Training batches: {len(train_loader):,}")
    print(f"📊 Validation batches: {len(val_loader):,}")
    
    # 4. Create model
    print("\n🤖 STEP 4: CREATING ENHANCED MODEL")
    model = CustomLanguageModel(
        vocab_size=vocab_size,
        dim=512,
        n_layers=8,
        n_heads=8,
        max_seq_len=256
    )
    
    # 5. Train model
    print("\n🚀 STEP 5: TRAINING MODEL")
    trainer = EnhancedModelTrainer(model, tokenizer)
    
    # Training configuration
    training_config = {
        'epochs': 15,
        'lr': 3e-4,
        'warmup_steps': 500,
        'save_every': 3,
        'generate_every': 5
    }
    
    print(f"⚙️ Training config: {training_config}")
    
    # Start training
    history = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        **training_config
    )
    
    # 6. Final evaluation and samples
    print("\n🎯 STEP 6: FINAL EVALUATION")
    
    test_prompts = [
        "The future of artificial intelligence",
        "In a world where",
        "Science has shown that",
        "Once upon a time",
        "The most important thing to understand"
    ]
    
    print("\n🎭 FINAL SAMPLE GENERATIONS:")
    for i, prompt in enumerate(test_prompts):
        print(f"\n--- Sample {i+1} ---")
        trainer.generate_sample(prompt, max_length=80, temperature=0.7)
    
    print("\n🎉 TRAINING PIPELINE COMPLETED SUCCESSFULLY!")
    print(f"📁 All files saved to: {PROJECT_DIR}")
    print(f"📊 Final training loss: {history['train_losses'][-1]:.4f}")
    if history['val_losses']:
        print(f"📊 Final validation loss: {history['val_losses'][-1]:.4f}")
    
    return {
        'model': model,
        'tokenizer': tokenizer,
        'trainer': trainer,
        'history': history
    }

# 🎯 INTERACTIVE TRAINING FUNCTION
def quick_training_demo():
    """Quick demo with smaller model for testing"""
    print("🚀 QUICK TRAINING DEMO")
    print("=" * 40)
    
    # Collect minimal data
    collector = DataCollector()
    texts = collector.collect_all_data(books=3, wiki_articles=5, news_articles=5)
    
    if not texts:
        print("❌ No data collected!")
        return None
    
    # Build tokenizer
    tokenizer = EnhancedTokenizer(vocab_size=10000)
    vocab_size = tokenizer.build_vocab(texts, min_freq=2)
    
    # Create dataset
    dataset = EnhancedTextDataset(texts, tokenizer, seq_len=128, stride=64)
    train_loader = DataLoader(dataset, batch_size=8, shuffle=True)
    
    # Small model
    model = CustomLanguageModel(
        vocab_size=vocab_size,
        dim=256,
        n_layers=4,
        n_heads=4,
        max_seq_len=128
    )
    
    # Quick training
    trainer = EnhancedModelTrainer(model, tokenizer)
    history = trainer.train(
        train_loader=train_loader,
        epochs=5,
        lr=1e-3,
        save_every=2,
        generate_every=2
    )
    
    return {'model': model, 'tokenizer': tokenizer, 'trainer': trainer}

# 💡 USAGE EXAMPLES AND INSTRUCTIONS
def show_usage_instructions():
    """Show how to use this enhanced training system"""
    
    instructions = """
    🌟 ENHANCED LANGUAGE MODEL TRAINER - USAGE GUIDE
    ================================================
    
    🚀 QUICK START (Google Colab):
    
    1. Run the setup cell first:
       ```python
       # Install packages and check GPU
       !pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
       !pip install tqdm matplotlib numpy requests beautifulsoup4 nltk
       ```
    
    2. Run full training pipeline:
       ```python
       results = main_training_pipeline()
       ```
    
    3. Or try quick demo:
       ```python
       demo_results = quick_training_demo()
       ```
    
    🎯 FEATURES:
    ✅ 50K+ vocabulary with subword tokenization
    ✅ Automatic data collection from free sources
    ✅ Advanced transformer architecture
    ✅ Mixed precision training
    ✅ Learning rate scheduling with warmup
    ✅ Automatic checkpointing and plotting
    ✅ Google Drive integration
    ✅ Text generation with sampling
    
    📊 DATA SOURCES:
    • Project Gutenberg (classic books)
    • Wikipedia articles
    • News RSS feeds
    
    🔧 CUSTOMIZATION:
    • Adjust vocab_size in EnhancedTokenizer
    • Change model architecture parameters
    • Modify training hyperparameters
    • Add your own text data
    
    📈 MONITORING:
    • Real-time training progress
    • Automatic loss plotting
    • Sample text generation
    • GPU memory tracking
    
    🎭 GENERATION:
    ```python
    trainer.generate_sample("Your prompt here", max_length=100)
    ```
    
    💾 SAVING/LOADING:
    • Models auto-saved to Google Drive
    • Tokenizers saved with vocabularies
    • Training history preserved
    """
    
    print(instructions)

def run_safe_demo():
    """Run a safe demo that handles all potential errors"""
    try:
        print("🚀 STARTING SAFE DEMO...")

        # First, ensure NLTK data is available
        if NLTK_AVAILABLE:
            try:
                import nltk
                nltk.download('punkt', quiet=True)
                nltk.download('punkt_tab', quiet=True)
                print("✅ NLTK data ready")
            except:
                print("⚠️ NLTK data download failed, using simple tokenization")

        # Run the demo
        demo_results = quick_training_demo()
        print("✅ Demo completed successfully!")
        return demo_results

    except Exception as e:
        print(f"❌ Demo failed: {e}")
        print("🔧 Trying basic training instead...")

        # Fallback to basic training with simple data
        try:
            basic_data = [
                "The quick brown fox jumps over the lazy dog.",
                "Machine learning is a subset of artificial intelligence.",
                "Neural networks can learn complex patterns from data.",
                "Deep learning uses multiple layers to process information."
            ] * 50  # Repeat for more data

            trainer, model, tokenizer, results = train_your_model(custom_data=basic_data)
            print("✅ Basic training completed!")
            return trainer, model, tokenizer, results

        except Exception as e2:
            print(f"❌ Basic training also failed: {e2}")
            print("🆘 Please check your environment setup")
            return None

# Display usage instructions
show_usage_instructions()

print("\n" + "="*60)
print("🎉 READY TO TRAIN YOUR OWN LANGUAGE MODEL!")
print("="*60)
print("🚀 Run train_your_model() to get started!")
print("⚡ Run quick_training_demo() for a fast test!")
print("🛡️ Run run_safe_demo() for error-safe testing!")
print("="*60)

if __name__ == "__main__":
    # Uncomment one of these to run:
    # results = main_training_pipeline()
    # demo_results = quick_training_demo()
    demo_results = run_safe_demo()