# 🛠️ IMPROVED TOKENIZER AND TEXT PROCESSING FOR BETTER ENGLISH

import torch
import re
import json
from datetime import datetime

# Import tiktoken for GPT-2 tokenizer
try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
    print("✅ tiktoken available - using GPT-2 tokenizer")
except ImportError:
    TIKTOKEN_AVAILABLE = False
    print("❌ tiktoken not available - install with: pip install tiktoken")

class GPT2TokenizerWrapper:
    """Wrapper around tiktoken GPT-2 tokenizer for consistent interface"""
    
    def __init__(self):
        if not TIKTOKEN_AVAILABLE:
            raise ImportError("tiktoken is required. Install with: pip install tiktoken")
        
        # Use GPT-2 tokenizer with full vocabulary (50,257 tokens)
        self.tokenizer = tiktoken.get_encoding("gpt2")
        self.vocab_size = self.tokenizer.n_vocab
        
        # Special tokens (GPT-2 uses different approach)
        self.pad_token_id = self.tokenizer.encode("<|endoftext|>")[0]  # Use endoftext as pad
        self.eos_token_id = self.tokenizer.encode("<|endoftext|>")[0]
        
        print(f"🔧 GPT-2 tokenizer initialized")
        print(f"📊 Vocabulary size: {self.vocab_size:,} tokens")
        print(f"🎯 This eliminates <UNK> tokens and improves text quality!")
    
    def encode(self, text):
        """Encode text to token IDs using GPT-2 tokenizer"""
        if not isinstance(text, str):
            text = str(text)
        
        # Clean and normalize text
        text = self.clean_text(text)
        
        # Encode using GPT-2 tokenizer
        token_ids = self.tokenizer.encode(text, allowed_special={"<|endoftext|>"})
        return token_ids
    
    def decode(self, token_ids):
        """Decode token IDs back to text"""
        if isinstance(token_ids, torch.Tensor):
            token_ids = token_ids.tolist()
        
        # Remove padding tokens if present
        if isinstance(token_ids, list) and self.pad_token_id in token_ids:
            # Remove padding tokens
            while token_ids and token_ids[-1] == self.pad_token_id:
                token_ids.pop()
        
        # Decode using GPT-2 tokenizer
        text = self.tokenizer.decode(token_ids)
        return text
    
    def clean_text(self, text):
        """Clean and normalize text for better training"""
        if not text or not isinstance(text, str):
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove control characters but keep basic punctuation
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)
        
        # Normalize quotes
        text = re.sub(r'["""]', '"', text)
        text = re.sub(r'[''']', "'", text)
        
        # Fix common encoding issues
        text = text.replace('â€™', "'")
        text = text.replace('â€œ', '"')
        text = text.replace('â€', '"')
        text = text.replace('â€"', '-')
        text = text.replace('â€"', '--')
        
        # Remove excessive punctuation
        text = re.sub(r'[.]{3,}', '...', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)
        
        return text.strip()
    
    def save(self, filename):
        """Save tokenizer info (GPT-2 tokenizer doesn't need saving)"""
        tokenizer_info = {
            'type': 'gpt2',
            'vocab_size': self.vocab_size,
            'pad_token_id': self.pad_token_id,
            'eos_token_id': self.eos_token_id,
            'created_at': datetime.now().isoformat()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(tokenizer_info, f, indent=2)
        
        print(f"💾 GPT-2 tokenizer info saved to {filename}")
        return filename
    
    def load(self, filename):
        """Load tokenizer info (GPT-2 tokenizer is always the same)"""
        print(f"📁 GPT-2 tokenizer loaded (info from {filename})")
        return filename

class ImprovedTextCleaner:
    """Enhanced text cleaning for better training data quality"""
    
    @staticmethod
    def clean_training_text(text):
        """Comprehensive text cleaning for training data"""
        if not text or not isinstance(text, str):
            return ""
        
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', text)
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove email addresses
        text = re.sub(r'\S+@\S+', '', text)
        
        # Remove excessive whitespace and newlines
        text = re.sub(r'\n\s*\n', '\n\n', text)  # Keep paragraph breaks
        text = re.sub(r'[ \t]+', ' ', text)  # Normalize spaces
        
        # Remove lines that are too short (likely noise)
        lines = text.split('\n')
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            if len(line) > 10:  # Keep lines with substantial content
                cleaned_lines.append(line)
        
        text = '\n'.join(cleaned_lines)
        
        # Remove excessive punctuation
        text = re.sub(r'[.]{4,}', '...', text)
        text = re.sub(r'[-]{3,}', '--', text)
        
        # Fix spacing around punctuation
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)
        text = re.sub(r'([,.!?;:])\s*([,.!?;:])', r'\1 \2', text)
        
        # Remove very long words (likely corrupted)
        words = text.split()
        cleaned_words = [word for word in words if len(word) < 50]
        text = ' '.join(cleaned_words)
        
        return text.strip()
    
    @staticmethod
    def filter_quality_text(texts, min_length=100, max_length=10000):
        """Filter texts by quality metrics"""
        quality_texts = []
        
        for text in texts:
            if not text or len(text) < min_length:
                continue
            
            if len(text) > max_length:
                # Split long texts into chunks
                chunks = [text[i:i+max_length] for i in range(0, len(text), max_length//2)]
                for chunk in chunks:
                    if len(chunk) >= min_length:
                        quality_texts.append(chunk)
            else:
                # Check text quality
                if ImprovedTextCleaner.is_quality_text(text):
                    quality_texts.append(text)
        
        return quality_texts
    
    @staticmethod
    def is_quality_text(text):
        """Check if text meets quality standards"""
        if not text or len(text) < 50:
            return False
        
        # Check for reasonable word count
        words = text.split()
        if len(words) < 10:
            return False
        
        # Check for reasonable sentence structure
        sentences = re.split(r'[.!?]+', text)
        if len(sentences) < 2:
            return False
        
        # Check for excessive repetition
        word_counts = {}
        for word in words:
            word_counts[word] = word_counts.get(word, 0) + 1
        
        # If any word appears more than 20% of the time, it's likely spam
        max_word_freq = max(word_counts.values()) / len(words)
        if max_word_freq > 0.2:
            return False
        
        # Check for reasonable character distribution
        alpha_chars = sum(1 for c in text if c.isalpha())
        if alpha_chars / len(text) < 0.5:  # At least 50% alphabetic
            return False
        
        return True
