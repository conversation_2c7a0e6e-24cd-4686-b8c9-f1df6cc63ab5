# 🚀 IMPROVED LANGUAGE MODEL TRAINER - FIXED FOR PROPER ENGLISH! 🚀
# Now with your custom TikToken tokenizer and enhanced training pipeline!
# Automatically downloads and cleans training data from free sources

# GOOGLE COLAB SETUP CELL - RUN THIS FIRST!
"""
# Uncomment and run this cell first in Google Colab:

# Install required packages
!pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
!pip install tqdm matplotlib numpy nltk beautifulsoup4 requests wikipedia-api tiktoken transformers tokenizers

# Check GPU
import torch
print(f"🔥 CUDA Available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"💾 GPU: {torch.cuda.get_device_name()}")
    print(f"🔥 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
import gc
from tqdm import tqdm
import matplotlib.pyplot as plt
import time
import json
import re
from collections import Counter, defaultdict
import shutil
from datetime import datetime
import requests
from urllib.parse import urljoin, urlparse
import random

# Import tiktoken and custom tokenizer
try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
    print("✅ tiktoken available - using improved tokenizer")
except ImportError:
    TIKTOKEN_AVAILABLE = False
    print("❌ tiktoken not available - install with: pip install tiktoken")

# Import the custom tokenizer from Tokenizer folder
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'Tokenizer'))

try:
    from tokenization import TikTokenTokenizer
    CUSTOM_TOKENIZER_AVAILABLE = True
    print("✅ Custom TikToken tokenizer available")
except ImportError:
    CUSTOM_TOKENIZER_AVAILABLE = False
    print("❌ Custom tokenizer not available")

# For text processing and data collection
try:
    import nltk
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    from nltk.tokenize import word_tokenize, sent_tokenize
    from nltk.corpus import stopwords
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False
    print("⚠️ NLTK not available - using basic tokenization")

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False
    print("⚠️ BeautifulSoup not available - can't scrape web data")

# Google Colab specific imports
try:
    from google.colab import drive, files
    IN_COLAB = True
    print("🔥 GOOGLE COLAB DETECTED!")
except ImportError:
    IN_COLAB = False
    print("🔥 RUNNING LOCALLY")

def check_environment():
    """Check if environment is properly set up"""
    print("🔍 ENVIRONMENT CHECK:")
    print(f"✅ Python version: {__import__('sys').version}")
    print(f"✅ PyTorch version: {torch.__version__}")
    print(f"✅ CUDA available: {torch.cuda.is_available()}")
    print(f"✅ NLTK available: {NLTK_AVAILABLE}")
    print(f"✅ BeautifulSoup available: {BS4_AVAILABLE}")
    print(f"✅ Custom tokenizer available: {CUSTOM_TOKENIZER_AVAILABLE}")

    if torch.cuda.is_available():
        print(f"✅ GPU: {torch.cuda.get_device_name()}")
        print(f"✅ GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

        # Test GPU
        try:
            test_tensor = torch.randn(100, 100).cuda()
            result = torch.mm(test_tensor, test_tensor)
            print("✅ GPU test passed!")
            del test_tensor, result
            torch.cuda.empty_cache()
        except Exception as e:
            print(f"❌ GPU test failed: {e}")
    else:
        print("⚠️ No GPU detected - training will be slow")

    print("✅ Environment check complete!")

# Run environment check
check_environment()

# 🛠️ IMPROVED TOKENIZER FOR PROPER ENGLISH GENERATION
class ImprovedTokenizerWrapper:
    """Wrapper around the custom TikToken tokenizer for better English generation"""
    
    def __init__(self):
        if not CUSTOM_TOKENIZER_AVAILABLE:
            # Fallback to basic tiktoken if custom tokenizer not available
            if not TIKTOKEN_AVAILABLE:
                raise ImportError("tiktoken is required. Install with: pip install tiktoken")
            
            print("⚠️ Using fallback GPT-2 tokenizer")
            self.tokenizer = tiktoken.get_encoding("gpt2")
            self.vocab_size = self.tokenizer.n_vocab
            self.pad_token_id = self.tokenizer.encode("<|endoftext|>")[0]
            self.eos_token_id = self.tokenizer.encode("<|endoftext|>")[0]
            self.is_custom = False
        else:
            # Use the improved custom tokenizer
            tokenizer_path = os.path.join(os.path.dirname(__file__), '..', 'Tokenizer')
            vocab_file = os.path.join(tokenizer_path, 'tiktoken.model')
            config_file = os.path.join(tokenizer_path, 'tokenizer_config.json')
            
            # Load config
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            self.tokenizer = TikTokenTokenizer(
                vocab_file=vocab_file,
                bos_token="[BOS]",
                eos_token="[EOS]",
                unk_token="[UNK]",
                pad_token="[PAD]",
                added_tokens_decoder=config.get('added_tokens_decoder', {})
            )
            self.vocab_size = self.tokenizer.vocab_size
            self.pad_token_id = self.tokenizer.pad_id
            self.eos_token_id = self.tokenizer.eos_id
            self.is_custom = True
        
        print(f"🔧 Improved tokenizer initialized")
        print(f"📊 Vocabulary size: {self.vocab_size:,} tokens")
        print(f"🎯 This provides better text quality and proper English generation!")
    
    def encode(self, text):
        """Encode text to token IDs"""
        if not isinstance(text, str):
            text = str(text)
        
        # Clean and normalize text
        text = self.clean_text(text)
        
        if self.is_custom:
            # Use custom tokenizer
            token_ids = self.tokenizer.encode(text, allow_special_tokens=True)
        else:
            # Use tiktoken fallback
            token_ids = self.tokenizer.encode(text, allowed_special={"<|endoftext|>"})
        
        return token_ids
    
    def decode(self, token_ids):
        """Decode token IDs back to text"""
        if isinstance(token_ids, torch.Tensor):
            token_ids = token_ids.tolist()
        
        # Remove padding tokens if present
        if isinstance(token_ids, list) and self.pad_token_id in token_ids:
            # Remove padding tokens
            while token_ids and token_ids[-1] == self.pad_token_id:
                token_ids.pop()
        
        # Decode using appropriate tokenizer
        if self.is_custom:
            text = self.tokenizer.decode(token_ids)
        else:
            text = self.tokenizer.decode(token_ids)
        
        return text
    
    def clean_text(self, text):
        """Clean and normalize text for better training"""
        if not text or not isinstance(text, str):
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove control characters but keep basic punctuation
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)
        
        # Normalize quotes
        text = re.sub(r'["""]', '"', text)
        text = re.sub(r'[''']', "'", text)
        
        # Fix common encoding issues
        text = text.replace('â€™', "'")
        text = text.replace('â€œ', '"')
        text = text.replace('â€', '"')
        text = text.replace('â€"', '-')
        text = text.replace('â€"', '--')
        
        # Remove excessive punctuation
        text = re.sub(r'[.]{3,}', '...', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)
        
        return text.strip()
    
    def save(self, filename):
        """Save tokenizer info"""
        tokenizer_info = {
            'type': 'custom' if self.is_custom else 'gpt2',
            'vocab_size': self.vocab_size,
            'pad_token_id': self.pad_token_id,
            'eos_token_id': self.eos_token_id,
            'is_custom': self.is_custom,
            'created_at': datetime.now().isoformat()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(tokenizer_info, f, indent=2)
        
        print(f"💾 Tokenizer info saved to {filename}")
        return filename
    
    def load(self, filename):
        """Load tokenizer info"""
        print(f"📁 Tokenizer loaded (info from {filename})")
        return filename

# 📚 ENHANCED TEXT DATASET FOR BETTER TRAINING
class EnhancedTextDataset(Dataset):
    """Enhanced dataset with better text processing"""
    
    def __init__(self, texts, tokenizer, seq_len=512, stride=256):
        self.tokenizer = tokenizer
        self.seq_len = seq_len
        self.stride = stride
        
        print(f"📚 Creating enhanced dataset...")
        print(f"📊 Processing {len(texts)} texts...")
        
        # Clean and filter texts
        cleaned_texts = []
        for text in tqdm(texts, desc="Cleaning texts"):
            cleaned = self.clean_training_text(text)
            if len(cleaned) > 100:  # Only keep substantial texts
                cleaned_texts.append(cleaned)
        
        print(f"📊 Kept {len(cleaned_texts)} quality texts after cleaning")
        
        # Tokenize all texts
        self.examples = []
        for text in tqdm(cleaned_texts, desc="Tokenizing"):
            tokens = tokenizer.encode(text)
            
            # Create sliding windows
            for i in range(0, len(tokens) - seq_len, stride):
                input_ids = tokens[i:i + seq_len]
                target_ids = tokens[i + 1:i + seq_len + 1]
                
                if len(input_ids) == seq_len and len(target_ids) == seq_len:
                    self.examples.append({
                        'input_ids': torch.tensor(input_ids, dtype=torch.long),
                        'target_ids': torch.tensor(target_ids, dtype=torch.long)
                    })
        
        print(f"📊 Created {len(self.examples)} training examples")
    
    def clean_training_text(self, text):
        """Clean text for training"""
        if not text or not isinstance(text, str):
            return ""
        
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', text)
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove email addresses
        text = re.sub(r'\S+@\S+', '', text)
        
        # Remove excessive whitespace and newlines
        text = re.sub(r'\n\s*\n', '\n\n', text)  # Keep paragraph breaks
        text = re.sub(r'[ \t]+', ' ', text)  # Normalize spaces
        
        # Remove lines that are too short (likely noise)
        lines = text.split('\n')
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            if len(line) > 10:  # Keep lines with substantial content
                cleaned_lines.append(line)
        
        text = '\n'.join(cleaned_lines)
        
        # Remove excessive punctuation
        text = re.sub(r'[.]{4,}', '...', text)
        text = re.sub(r'[-]{3,}', '--', text)
        
        # Fix spacing around punctuation
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)
        text = re.sub(r'([,.!?;:])\s*([,.!?;:])', r'\1 \2', text)
        
        # Remove very long words (likely corrupted)
        words = text.split()
        cleaned_words = [word for word in words if len(word) < 50]
        text = ' '.join(cleaned_words)
        
        return text.strip()
    
    def __len__(self):
        return len(self.examples)
    
    def __getitem__(self, idx):
        return self.examples[idx]

# 🤖 ENHANCED MODEL ARCHITECTURE
class MultiHeadAttention(nn.Module):
    def __init__(self, dim, n_heads):
        super().__init__()
        self.dim = dim
        self.n_heads = n_heads
        self.head_dim = dim // n_heads

        self.qkv = nn.Linear(dim, dim * 3)
        self.out_proj = nn.Linear(dim, dim)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        B, T, C = x.shape

        # Get Q, K, V
        qkv = self.qkv(x).reshape(B, T, 3, self.n_heads, self.head_dim)
        qkv = qkv.permute(2, 0, 3, 1, 4)  # (3, B, n_heads, T, head_dim)
        q, k, v = qkv[0], qkv[1], qkv[2]

        # Attention
        att = (q @ k.transpose(-2, -1)) * (1.0 / np.sqrt(self.head_dim))
        att = F.softmax(att, dim=-1)
        att = self.dropout(att)

        # Apply attention to values
        y = att @ v  # (B, n_heads, T, head_dim)
        y = y.transpose(1, 2).contiguous().view(B, T, C)  # (B, T, C)

        # Output projection
        y = self.out_proj(y)
        return y

class TransformerBlock(nn.Module):
    def __init__(self, dim, n_heads):
        super().__init__()
        self.attn = MultiHeadAttention(dim, n_heads)
        self.mlp = nn.Sequential(
            nn.Linear(dim, dim * 4),
            nn.GELU(),
            nn.Linear(dim * 4, dim),
            nn.Dropout(0.1)
        )
        self.ln1 = nn.LayerNorm(dim)
        self.ln2 = nn.LayerNorm(dim)

    def forward(self, x):
        x = x + self.attn(self.ln1(x))
        x = x + self.mlp(self.ln2(x))
        return x

class ImprovedLanguageModel(nn.Module):
    """Enhanced language model with better architecture"""

    def __init__(self, vocab_size, dim=512, n_layers=8, n_heads=8, max_seq_len=512):
        super().__init__()
        self.vocab_size = vocab_size
        self.dim = dim
        self.max_seq_len = max_seq_len

        # Embeddings
        self.tok_emb = nn.Embedding(vocab_size, dim)
        self.pos_emb = nn.Embedding(max_seq_len, dim)
        self.dropout = nn.Dropout(0.1)

        # Transformer blocks
        self.blocks = nn.ModuleList([
            TransformerBlock(dim, n_heads) for _ in range(n_layers)
        ])

        self.ln_f = nn.LayerNorm(dim)
        self.head = nn.Linear(dim, vocab_size)

        # Initialize weights
        self.apply(self._init_weights)

        print(f"🤖 Enhanced model created: {self.count_params():,} parameters")
        print(f"📐 Architecture: {n_layers} layers, {n_heads} heads, {dim} dim")

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)

    def count_params(self):
        return sum(p.numel() for p in self.parameters())

    def forward(self, x, targets=None):
        B, T = x.shape

        # Get embeddings
        tok_emb = self.tok_emb(x)
        pos_emb = self.pos_emb(torch.arange(T, device=x.device))
        x = self.dropout(tok_emb + pos_emb)

        # Apply transformer blocks
        for block in self.blocks:
            x = block(x)

        x = self.ln_f(x)
        logits = self.head(x)

        loss = None
        if targets is not None:
            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=-1)

        return logits, loss

# 🚀 ENHANCED TRAINER WITH BETTER GENERATION
class ImprovedModelTrainer:
    """Enhanced trainer with better text generation capabilities"""

    def __init__(self, model, tokenizer, device=None):
        self.model = model
        self.tokenizer = tokenizer
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        self.model.to(self.device)

        # Training metrics
        self.train_losses = []
        self.val_losses = []

        print(f"🚀 Enhanced trainer initialized")
        print(f"🖥️  Device: {self.device}")

    def train_epoch(self, dataloader, optimizer, epoch):
        """Train for one epoch"""
        self.model.train()
        total_loss = 0

        pbar = tqdm(dataloader, desc=f"Epoch {epoch+1}")
        for batch_idx, batch in enumerate(pbar):
            input_ids = batch['input_ids'].to(self.device)
            target_ids = batch['target_ids'].to(self.device)

            optimizer.zero_grad()

            # Forward pass
            logits, loss = self.model(input_ids, target_ids)

            # Backward pass
            loss.backward()

            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)

            optimizer.step()

            total_loss += loss.item()

            # Update progress bar
            pbar.set_postfix({'loss': f'{loss.item():.4f}'})

            # Memory cleanup
            if batch_idx % 100 == 0:
                torch.cuda.empty_cache()

        avg_loss = total_loss / len(dataloader)
        self.train_losses.append(avg_loss)

        return avg_loss

    @torch.no_grad()
    def validate(self, dataloader):
        """Validate the model"""
        self.model.eval()
        total_loss = 0

        for batch in tqdm(dataloader, desc="Validating"):
            input_ids = batch['input_ids'].to(self.device)
            target_ids = batch['target_ids'].to(self.device)

            logits, loss = self.model(input_ids, target_ids)
            total_loss += loss.item()

        avg_loss = total_loss / len(dataloader)
        self.val_losses.append(avg_loss)

        return avg_loss

    @torch.no_grad()
    def generate_sample(self, prompt="Hello, how are you?", max_length=100, temperature=0.8, top_k=50, top_p=0.9):
        """Generate sample text with improved sampling"""
        self.model.eval()

        print(f"\n🎭 GENERATING SAMPLE TEXT:")
        print(f"📝 Prompt: '{prompt}'")

        # Encode prompt
        tokens = self.tokenizer.encode(prompt)
        input_ids = torch.tensor([tokens], dtype=torch.long, device=self.device)

        generated_tokens = tokens.copy()

        for _ in range(max_length):
            if input_ids.size(1) >= self.model.max_seq_len:
                input_ids = input_ids[:, -self.model.max_seq_len+1:]

            # Forward pass
            logits, _ = self.model(input_ids)

            # Apply temperature and sampling
            logits = logits[:, -1, :] / temperature

            # Top-k filtering
            if top_k > 0:
                v, _ = torch.topk(logits, min(top_k, logits.size(-1)))
                logits[logits < v[:, [-1]]] = -float('inf')

            # Top-p (nucleus) filtering
            if top_p < 1.0:
                sorted_logits, sorted_indices = torch.sort(logits, descending=True)
                cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)

                # Remove tokens with cumulative probability above the threshold
                sorted_indices_to_remove = cumulative_probs > top_p
                sorted_indices_to_remove[..., 1:] = sorted_indices_to_remove[..., :-1].clone()
                sorted_indices_to_remove[..., 0] = 0

                indices_to_remove = sorted_indices_to_remove.scatter(1, sorted_indices, sorted_indices_to_remove)
                logits[indices_to_remove] = -float('inf')

            # Sample next token
            probs = F.softmax(logits, dim=-1)
            next_token = torch.multinomial(probs, num_samples=1)

            generated_tokens.append(next_token.item())
            input_ids = torch.cat([input_ids, next_token], dim=1)

            # Stop at end token
            if next_token.item() == self.tokenizer.eos_token_id:
                break

        generated_text = self.tokenizer.decode(generated_tokens)
        print(f"🎯 Generated: '{generated_text}'")
        print("-" * 60)

        return generated_text

    def save_checkpoint(self, filepath, epoch, optimizer, loss):
        """Save training checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'loss': loss,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'vocab_size': self.model.vocab_size,
            'model_config': {
                'vocab_size': self.model.vocab_size,
                'dim': self.model.dim,
                'max_seq_len': self.model.max_seq_len
            }
        }

        torch.save(checkpoint, filepath)
        print(f"💾 Checkpoint saved: {filepath}")

# 📚 ENHANCED DATA COLLECTION (from original MainTrainCode.py)
class DataCollector:
    """Enhanced data collector with better text quality"""

    def __init__(self, data_dir="./training_data"):
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
        self.collected_texts = []

    def get_gutenberg_books(self, max_books=5):
        """Download free books from Project Gutenberg"""
        print("📚 Downloading books from Project Gutenberg...")

        book_ids = [
            1342,  # Pride and Prejudice
            11,    # Alice's Adventures in Wonderland
            74,    # Adventures of Tom Sawyer
            84,    # Frankenstein
            1661,  # Sherlock Holmes
            2701,  # Moby Dick
            16,    # Peter Pan
            1400,  # Great Expectations
            5200,  # Metamorphosis
            844,   # The Importance of Being Earnest
        ]

        texts = []
        for book_id in book_ids[:max_books]:
            try:
                url = f"https://www.gutenberg.org/files/{book_id}/{book_id}-0.txt"
                response = requests.get(url, timeout=10)

                if response.status_code == 200:
                    text = response.text

                    # Clean Gutenberg text
                    start_markers = [
                        "*** START OF THE PROJECT GUTENBERG EBOOK",
                        "*** START OF THIS PROJECT GUTENBERG EBOOK"
                    ]
                    end_markers = [
                        "*** END OF THE PROJECT GUTENBERG EBOOK",
                        "*** END OF THIS PROJECT GUTENBERG EBOOK"
                    ]

                    # Find start
                    start_idx = 0
                    for marker in start_markers:
                        idx = text.find(marker)
                        if idx != -1:
                            start_idx = text.find('\n', idx) + 1
                            break

                    # Find end
                    end_idx = len(text)
                    for marker in end_markers:
                        idx = text.find(marker)
                        if idx != -1:
                            end_idx = idx
                            break

                    if start_idx < end_idx:
                        clean_text = text[start_idx:end_idx].strip()
                        if len(clean_text) > 1000:
                            texts.append(clean_text)
                            print(f"✅ Downloaded book {book_id} ({len(clean_text):,} chars)")

                time.sleep(1)  # Be respectful

            except Exception as e:
                print(f"⚠️ Failed to download book {book_id}: {e}")

        return texts

    def collect_sample_data(self):
        """Collect high-quality sample data for training"""
        print("📊 Collecting high-quality training data...")

        # Get books from Project Gutenberg
        texts = self.get_gutenberg_books(max_books=3)

        # Add some high-quality sample text
        sample_texts = [
            """
            The art of conversation is a skill that has been valued throughout human history.
            Good conversation involves listening carefully, asking thoughtful questions, and
            sharing ideas in a clear and engaging manner. When people communicate effectively,
            they build stronger relationships and create meaningful connections.

            In today's digital age, the ability to have genuine conversations has become even
            more important. While technology provides many ways to connect, nothing replaces
            the value of authentic human interaction. Whether in person or through digital
            means, good communication skills remain essential for success in both personal
            and professional relationships.
            """,
            """
            Science and technology continue to advance at an unprecedented pace, bringing
            both opportunities and challenges. Artificial intelligence, renewable energy,
            and biotechnology are just a few areas where rapid progress is being made.
            These developments have the potential to solve many of humanity's greatest
            challenges, from climate change to disease.

            However, with great power comes great responsibility. As we develop new
            technologies, we must also consider their ethical implications and ensure
            they benefit all of humanity. The future depends on our ability to harness
            these powerful tools wisely and responsibly.
            """,
            """
            Education is the foundation of human progress and development. Through learning,
            individuals acquire knowledge, develop critical thinking skills, and prepare
            for meaningful careers. A good education system should be accessible to all,
            regardless of background or circumstances.

            The best educational experiences combine theoretical knowledge with practical
            application. Students learn not just facts and figures, but how to think
            creatively, solve problems, and work collaboratively with others. These skills
            are essential for success in an ever-changing world.
            """
        ]

        texts.extend(sample_texts)

        print(f"📊 Collected {len(texts)} high-quality texts")
        total_chars = sum(len(text) for text in texts)
        print(f"📊 Total characters: {total_chars:,}")

        self.collected_texts = texts
        return texts

# 🚀 MAIN TRAINING PIPELINE
def improved_training_pipeline():
    """Complete improved training pipeline"""

    print("🌟 IMPROVED LANGUAGE MODEL TRAINING PIPELINE")
    print("=" * 70)

    # 1. Initialize tokenizer
    print("\n🔧 STEP 1: INITIALIZING IMPROVED TOKENIZER")
    tokenizer = ImprovedTokenizerWrapper()

    # 2. Collect training data
    print("\n📊 STEP 2: COLLECTING TRAINING DATA")
    collector = DataCollector()
    texts = collector.collect_sample_data()

    if not texts:
        print("❌ No training data collected!")
        return None

    # 3. Create datasets
    print("\n📚 STEP 3: CREATING ENHANCED DATASETS")

    # Split data into train/val
    split_idx = int(0.9 * len(texts))
    train_texts = texts[:split_idx] if split_idx > 0 else texts
    val_texts = texts[split_idx:] if len(texts) > 1 else texts[-1:]

    # Create datasets with improved processing
    train_dataset = EnhancedTextDataset(train_texts, tokenizer, seq_len=256, stride=128)
    val_dataset = EnhancedTextDataset(val_texts, tokenizer, seq_len=256, stride=256)

    # Create data loaders
    batch_size = 8 if torch.cuda.is_available() else 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)

    print(f"📊 Training batches: {len(train_loader):,}")
    print(f"📊 Validation batches: {len(val_loader):,}")

    # 4. Create improved model
    print("\n🤖 STEP 4: CREATING IMPROVED MODEL")
    model = ImprovedLanguageModel(
        vocab_size=tokenizer.vocab_size,
        dim=512,
        n_layers=6,  # Smaller for faster training
        n_heads=8,
        max_seq_len=256
    )

    # 5. Train model
    print("\n🚀 STEP 5: TRAINING IMPROVED MODEL")
    trainer = ImprovedModelTrainer(model, tokenizer)

    # Setup optimizer
    optimizer = torch.optim.AdamW(model.parameters(), lr=3e-4, weight_decay=0.01)

    # Training loop
    num_epochs = 5  # Start with fewer epochs for testing

    print(f"🎯 Starting training for {num_epochs} epochs...")

    for epoch in range(num_epochs):
        print(f"\n📈 Epoch {epoch+1}/{num_epochs}")

        # Train
        train_loss = trainer.train_epoch(train_loader, optimizer, epoch)
        print(f"📊 Training loss: {train_loss:.4f}")

        # Validate
        if len(val_loader) > 0:
            val_loss = trainer.validate(val_loader)
            print(f"📊 Validation loss: {val_loss:.4f}")

        # Generate sample
        if epoch % 2 == 0:  # Generate every 2 epochs
            trainer.generate_sample("Hello, how are you?", max_length=50)

        # Save checkpoint
        checkpoint_path = f"improved_model_epoch_{epoch+1}.pt"
        trainer.save_checkpoint(checkpoint_path, epoch, optimizer, train_loss)

    print("\n🎉 Training completed!")
    print("🎯 Final test generation:")
    trainer.generate_sample("The future of artificial intelligence is", max_length=100)

    return trainer

print("✅ Improved training code loaded!")
print("🎯 Key improvements:")
print("   • Uses your custom TikToken tokenizer")
print("   • Better text cleaning and preprocessing")
print("   • Enhanced dataset creation")
print("   • Proper vocabulary handling")
print("   • Eliminates <UNK> tokens")
print("   • Improved model architecture")
print("   • Enhanced generation with temperature, top-k, top-p")
print("   • Complete training pipeline")
print("\n🚀 Ready for training with better English generation!")
print("\n📋 To start training, run:")
print("   trainer = improved_training_pipeline()")
