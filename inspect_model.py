#!/usr/bin/env python3
"""
Quick script to inspect your custom model file and show its configuration.
"""

import torch
import json
from pathlib import Path

def inspect_model():
    """Inspect the custom model file."""
    model_path = Path("models/featureimanbug_ai_model.pt")
    
    if not model_path.exists():
        print("❌ Model file not found!")
        return
    
    print("🔍 INSPECTING CUSTOM MODEL")
    print("=" * 50)
    
    try:
        # Load checkpoint
        checkpoint = torch.load(model_path, map_location='cpu')
        
        print("📦 Checkpoint contents:")
        for key in checkpoint.keys():
            if isinstance(checkpoint[key], dict):
                print(f"  📁 {key}: {len(checkpoint[key])} items")
            else:
                print(f"  📄 {key}: {type(checkpoint[key])}")
        
        # Check model config
        if 'model_config' in checkpoint:
            config = checkpoint['model_config']
            print(f"\n🤖 MODEL CONFIGURATION:")
            for key, value in config.items():
                print(f"  • {key}: {value}")
        
        # Check if there's a regular config
        if 'config' in checkpoint:
            config = checkpoint['config']
            print(f"\n⚙️ REGULAR CONFIG:")
            for key, value in config.items():
                print(f"  • {key}: {value}")
        
        # Check model state dict structure
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
            print(f"\n🏗️ MODEL STRUCTURE:")
            
            # Look for key layers to understand architecture
            for key in list(state_dict.keys())[:10]:  # First 10 keys
                shape = state_dict[key].shape if hasattr(state_dict[key], 'shape') else 'scalar'
                print(f"  • {key}: {shape}")
            
            if len(state_dict) > 10:
                print(f"  ... and {len(state_dict) - 10} more layers")
        
        # Check for training info
        if 'losses_history' in checkpoint:
            losses = checkpoint['losses_history']
            print(f"\n📊 TRAINING INFO:")
            print(f"  • Training epochs: {len(losses)}")
            print(f"  • Final loss: {losses[-1]:.4f}")
            print(f"  • Best loss: {min(losses):.4f}")
        
    except Exception as e:
        print(f"❌ Error inspecting model: {e}")

def suggest_fix():
    """Suggest how to fix the model loading issue."""
    print("\n🔧 SUGGESTED FIX:")
    print("=" * 50)
    print("The error suggests your model has incompatible dimensions.")
    print("Your custom model architecture doesn't match the FeatureimanbugModel.")
    print("\nOptions:")
    print("1. 🔄 Create a compatible model loader")
    print("2. 🏗️ Modify the FeatureimanbugModel to handle your architecture")
    print("3. 🎯 Use a generic model loader")
    print("\nI'll create a fix for option 1...")

if __name__ == "__main__":
    inspect_model()
    suggest_fix()
