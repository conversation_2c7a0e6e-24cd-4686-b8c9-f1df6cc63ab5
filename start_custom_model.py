#!/usr/bin/env python3
"""
Enhanced startup script for Featureimanbug AI with custom model support.
Automatically detects and loads your trained model and tokenizer.
"""

import os
import sys
import json
import argparse
from pathlib import Path

def check_model_files():
    """Check for custom model files and provide helpful information."""
    print("🔍 CHECKING FOR CUSTOM MODEL FILES...")
    print("=" * 50)
    
    models_dir = Path("models")
    model_file = models_dir / "featureimanbug_ai_model.pt"
    tokenizer_file = models_dir / "tokenizer.json"
    
    # Check if models directory exists
    if not models_dir.exists():
        print("❌ Models directory not found!")
        print("📁 Creating models/ directory...")
        models_dir.mkdir(exist_ok=True)
        
        print("\n📋 TO USE YOUR CUSTOM MODEL:")
        print("1. Download your trained model from Google Drive")
        print("2. Rename it to: models/featureimanbug_ai_model.pt")
        print("3. Download your tokenizer file")
        print("4. Rename it to: models/tokenizer.json")
        print("5. Run this script again")
        return False, None, None
    
    # Check model file
    model_exists = model_file.exists()
    tokenizer_exists = tokenizer_file.exists()
    
    print(f"🤖 Model file: {'✅ Found' if model_exists else '❌ Missing'} ({model_file})")
    print(f"📚 Tokenizer file: {'✅ Found' if tokenizer_exists else '❌ Missing'} ({tokenizer_file})")
    
    if model_exists and tokenizer_exists:
        # Get model info
        try:
            import torch
            checkpoint = torch.load(model_file, map_location='cpu')
            
            if 'model_config' in checkpoint:
                config = checkpoint['model_config']
                print(f"\n📊 MODEL INFORMATION:")
                print(f"   • Vocabulary size: {config.get('vocab_size', 'Unknown'):,}")
                print(f"   • Model dimension: {config.get('dim', 'Unknown')}")
                print(f"   • Layers: {config.get('n_layers', 'Unknown')}")
                print(f"   • Attention heads: {config.get('n_heads', 'Unknown')}")
                print(f"   • Max sequence length: {config.get('max_seq_len', 'Unknown')}")
            
            # Get tokenizer info
            with open(tokenizer_file, 'r', encoding='utf-8') as f:
                tokenizer_data = json.load(f)
            
            print(f"\n📚 TOKENIZER INFORMATION:")
            print(f"   • Vocabulary size: {tokenizer_data.get('vocab_size', 'Unknown'):,}")
            print(f"   • Special tokens: {len(tokenizer_data.get('special_tokens', {}))}")
            if 'created_at' in tokenizer_data:
                print(f"   • Created: {tokenizer_data['created_at']}")
            
            print("\n✅ CUSTOM MODEL READY!")
            return True, model_file, tokenizer_file
            
        except Exception as e:
            print(f"⚠️ Error reading model files: {e}")
            return False, model_file, tokenizer_file
    
    else:
        print("\n❌ CUSTOM MODEL NOT READY")
        if not model_exists:
            print("   • Missing: featureimanbug_ai_model.pt")
        if not tokenizer_exists:
            print("   • Missing: tokenizer.json")
        
        print("\n📋 SETUP INSTRUCTIONS:")
        print("1. Train your model using MainTrainCode.py in Google Colab")
        print("2. Download the trained files from Google Drive:")
        print("   - best_model.pt → models/featureimanbug_ai_model.pt")
        print("   - latest_tokenizer.json → models/tokenizer.json")
        print("3. Run this script again")
        
        return False, None, None

def start_web_interface(model_path=None):
    """Start the web interface with optional model path."""
    print("\n🚀 STARTING WEB INTERFACE...")
    print("=" * 50)

    # Import and start the web server
    try:
        from web_server import main as start_server

        # Prepare arguments - use port 8080 to avoid macOS AirPlay conflict
        sys.argv = ['start_custom_model.py', '--port', '8080']
        if model_path:
            sys.argv.extend(['--model-path', str(model_path)])

        # Start the server
        start_server()
        
    except KeyboardInterrupt:
        print("\n👋 Shutting down...")
    except Exception as e:
        print(f"❌ Error starting web interface: {e}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Start Featureimanbug AI with custom model')
    parser.add_argument('--force-check', action='store_true', help='Force model file check')
    parser.add_argument('--no-auto-start', action='store_true', help='Check files but don\'t start server')
    
    args = parser.parse_args()
    
    print("🤖 FEATUREIMANBUG AI - CUSTOM MODEL LAUNCHER")
    print("=" * 60)
    
    # Check model files
    model_ready, model_path, tokenizer_path = check_model_files()
    
    if args.no_auto_start:
        print("\n✅ File check complete. Use --no-auto-start=false to start server.")
        return
    
    if model_ready:
        print(f"\n🎯 Starting with custom model: {model_path}")
        print("🌐 Web interface will be available at: http://localhost:8080")
        start_web_interface(model_path)
    else:
        print("\n⚠️ No custom model found. Starting with default settings...")
        response = input("Continue anyway? (y/n): ").lower().strip()
        if response == 'y':
            print("🌐 Web interface will be available at: http://localhost:8080")
            start_web_interface()
        else:
            print("👋 Setup your custom model and try again!")

if __name__ == "__main__":
    main()
