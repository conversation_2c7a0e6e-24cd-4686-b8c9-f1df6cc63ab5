# Featureimanbug AI - Custom Model Requirements
# Install with: pip install -r requirements_custom.txt

# Core ML libraries
torch>=2.0.0
numpy>=1.21.0
tqdm>=4.64.0

# Web interface
flask>=2.3.0
flask-socketio>=5.3.0
python-socketio>=5.8.0

# Data processing
beautifulsoup4>=4.12.0
requests>=2.31.0
nltk>=3.8.0

# Visualization
matplotlib>=3.7.0

# Text processing
tiktoken>=0.4.0

# Development tools (optional)
jupyter>=1.0.0
ipython>=8.0.0

# For Google Colab compatibility
google-colab; sys_platform == "linux"
