#!/usr/bin/env python3
"""
Debug tokenizer loading issue
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_tokenizer_loading():
    """Test the tokenizer loading directly."""
    print("🔍 DEBUGGING TOKENIZER LOADING")
    print("=" * 50)
    
    try:
        from src.web.model_manager import ModelManager
        
        model_manager = ModelManager()
        
        print("🔄 Testing custom tokenizer loading...")
        custom_tokenizer = model_manager._load_custom_tokenizer()
        
        if custom_tokenizer:
            print("✅ Custom tokenizer loaded successfully!")
            print(f"📚 Vocab size: {custom_tokenizer.vocab_size}")
            print(f"🔧 Special tokens: {list(custom_tokenizer.special_tokens.keys())}")
            
            # Test encoding/decoding
            test_text = "hello world"
            encoded = custom_tokenizer.encode(test_text)
            decoded = custom_tokenizer.decode(encoded)
            print(f"🧪 Test: '{test_text}' -> {encoded} -> '{decoded}'")
            
        else:
            print("❌ Custom tokenizer failed to load")
            
        print("\n🔄 Testing default tokenizer loading...")
        default_tokenizer = model_manager._load_default_tokenizer()
        
        if default_tokenizer:
            print("✅ Default tokenizer loaded successfully!")
            print(f"📚 Type: {type(default_tokenizer)}")
        else:
            print("❌ Default tokenizer failed to load")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tokenizer_loading()
