#!/usr/bin/env python3
"""
Test script to verify custom model loading works correctly.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_model_loading():
    """Test loading the custom model."""
    print("🧪 TESTING CUSTOM MODEL LOADING")
    print("=" * 50)
    
    try:
        # Import the model loader
        from src.model.custom_model import load_custom_model
        
        model_path = "models/featureimanbug_ai_model.pt"
        
        print(f"📂 Loading model from: {model_path}")
        model, config = load_custom_model(model_path, device='cpu')
        
        print("✅ Model loaded successfully!")
        print(f"📊 Model parameters: {model.get_num_params():,}")
        print(f"📐 Model dimension: {model.dim}")
        print(f"🔢 Vocabulary size: {model.vocab_size:,}")
        print(f"📏 Max sequence length: {model.max_seq_len}")
        print(f"🧠 Number of layers: {model.n_layers}")
        print(f"👁️ Number of heads: {model.n_heads}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tokenizer_loading():
    """Test loading the custom tokenizer."""
    print("\n🧪 TESTING CUSTOM TOKENIZER LOADING")
    print("=" * 50)
    
    try:
        import json
        
        tokenizer_path = "models/tokenizer.json"
        
        print(f"📚 Loading tokenizer from: {tokenizer_path}")
        
        with open(tokenizer_path, 'r', encoding='utf-8') as f:
            tokenizer_data = json.load(f)
        
        # Create simple tokenizer wrapper
        class SimpleTokenizer:
            def __init__(self, data):
                self.word_to_id = data['word_to_id']
                self.id_to_word = {int(k): v for k, v in data['id_to_word'].items()}
                self.vocab_size = data['vocab_size']
                self.special_tokens = data['special_tokens']
            
            def encode(self, text):
                words = text.lower().split()
                return [self.word_to_id.get(word, self.special_tokens['<UNK>']) for word in words]
            
            def decode(self, token_ids):
                words = []
                for token_id in token_ids:
                    if isinstance(token_id, int):
                        word = self.id_to_word.get(token_id, '<UNK>')
                        if word not in ['<PAD>', '< SOS >', '<EOS>']:
                            words.append(word)
                return ' '.join(words)
        
        tokenizer = SimpleTokenizer(tokenizer_data)
        
        print("✅ Tokenizer loaded successfully!")
        print(f"📚 Vocabulary size: {tokenizer.vocab_size:,}")
        print(f"🔧 Special tokens: {list(tokenizer.special_tokens.keys())}")
        
        # Test encoding/decoding
        test_text = "Hello world machine learning"
        encoded = tokenizer.encode(test_text)
        decoded = tokenizer.decode(encoded)
        
        print(f"\n🧪 Test encoding/decoding:")
        print(f"   Input: '{test_text}'")
        print(f"   Encoded: {encoded}")
        print(f"   Decoded: '{decoded}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Tokenizer loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_model_manager():
    """Test the web model manager with custom model."""
    print("\n🧪 TESTING WEB MODEL MANAGER")
    print("=" * 50)
    
    try:
        from src.web.model_manager import ModelManager
        
        model_manager = ModelManager()
        model_path = "models/featureimanbug_ai_model.pt"
        
        print(f"🌐 Testing model manager with: {model_path}")
        
        # This would normally be async, but we'll test the core loading logic
        model, config, tokenizer = model_manager._load_featureimanbug_model(model_path)
        
        print("✅ Model manager test successful!")
        print(f"📊 Model loaded: {model is not None}")
        print(f"⚙️ Config loaded: {config is not None}")
        print(f"📚 Tokenizer loaded: {tokenizer is not None}")
        
        if tokenizer:
            print(f"📚 Tokenizer vocab size: {getattr(tokenizer, 'vocab_size', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 CUSTOM MODEL COMPATIBILITY TESTS")
    print("=" * 60)
    
    results = []
    
    # Test 1: Model loading
    results.append(test_model_loading())
    
    # Test 2: Tokenizer loading
    results.append(test_tokenizer_loading())
    
    # Test 3: Web model manager
    results.append(test_web_model_manager())
    
    # Summary
    print("\n📋 TEST SUMMARY")
    print("=" * 50)
    
    test_names = ["Model Loading", "Tokenizer Loading", "Web Model Manager"]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
    
    all_passed = all(results)
    
    if all_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("Your custom model is ready for the web interface!")
    else:
        print("\n⚠️ SOME TESTS FAILED")
        print("Check the error messages above for details.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
