"""
Featureimanbug AI - Model manager for web interface.
"""
import torch
import os
from typing import Optional, Dict, Any
import threading
import time

from ..model.featureimanbug_model import FeatureimanbugModel, FeatureimanbugConfig
from ..generation.generator import TextGenerator
from ..chat.interface import ChatInterface


class ModelManager:
    """Manages model loading and inference for the web interface."""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.generator = None
        self.chat_interface = None
        self.config = None
        self.device = None
        self.model_info = {}
        self.loading = False
        self.load_error = None
        
        # Optimal preset settings for natural conversation
        self.generation_settings = {
            'max_new_tokens': 100,
            'strategy': 'temperature',
            'temperature': 0.8,
            'top_k': 50,
            'top_p': 0.9,
            'repetition_penalty': 1.1,
            'stream': True
        }
    
    def load_model_async(self, model_path: str, model_type: str = "auto"):
        """Load model asynchronously to avoid blocking the web interface."""
        def load_worker():
            try:
                self.loading = True
                self.load_error = None
                print(f"🔄 Loading model from {model_path}")

                # Determine device
                self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
                print(f"🖥️  Using device: {self.device}")

                # Load Featureimanbug AI model
                self.model, self.config, self.tokenizer = self._load_featureimanbug_model(model_path)

                # Debug: Check what we got
                print(f"🔍 Debug - Model: {self.model is not None}")
                print(f"🔍 Debug - Config: {self.config is not None}")
                print(f"🔍 Debug - Tokenizer: {self.tokenizer is not None}")
                if self.tokenizer:
                    print(f"🔍 Debug - Tokenizer type: {type(self.tokenizer)}")
                    print(f"🔍 Debug - Tokenizer vocab: {getattr(self.tokenizer, 'vocab_size', 'Unknown')}")

                # Create generator
                self.generator = TextGenerator(self.model, self.tokenizer)
                
                # Create chat interface
                self.chat_interface = ChatInterface(
                    model=self.model,
                    generator=self.generator,
                    history_dir="web_chat_history",
                    auto_save=True
                )
                
                # Update generation settings
                self.chat_interface.update_generation_settings(**self.generation_settings)
                
                # Store model info
                self.model_info = {
                    'model_type': 'GPT-2 355M',
                    'parameters': self.model.get_num_params(),
                    'layers': self.config.n_layers,
                    'embedding_dim': self.config.emb_dim,
                    'attention_heads': self.config.n_heads,
                    'context_length': self.config.context_length,
                    'vocab_size': self.config.vocab_size,
                    'device': str(self.device),
                    'loaded_at': time.strftime('%Y-%m-%d %H:%M:%S')
                }
                
                print("✅ Model loaded successfully!")
                print(f"📊 Model info: {self.model_info['parameters']:,} parameters")
                
                # Test generation
                self._test_generation()
                
            except Exception as e:
                self.load_error = str(e)
                print(f"❌ Model loading failed: {e}")
                import traceback
                traceback.print_exc()
            finally:
                self.loading = False
        
        # Start loading in background thread
        thread = threading.Thread(target=load_worker, daemon=True)
        thread.start()

    def _load_featureimanbug_model(self, model_path: str):
        """Load a Featureimanbug AI model with custom tokenizer support."""
        print(f"📂 Loading Featureimanbug AI model from: {model_path}")

        # Ensure device is set correctly
        if not hasattr(self, 'device') or self.device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Load checkpoint to check type
        checkpoint = torch.load(model_path, map_location=self.device)

        # Check if this is a custom model from MainTrainCode.py
        if 'model_config' in checkpoint and 'dim' in checkpoint['model_config']:
            print("🎯 Detected custom model from MainTrainCode.py")
            return self._load_custom_model(model_path, checkpoint)

        # Otherwise try to load as standard FeatureimanbugModel
        print("🔧 Loading as standard FeatureimanbugModel")
        return self._load_standard_model(model_path, checkpoint)

    def _load_custom_model(self, model_path: str, checkpoint: dict):
        """Load custom model from MainTrainCode.py"""
        from ..model.custom_model import load_custom_model

        try:
            model, config = load_custom_model(model_path, self.device)

            # Create a config object for compatibility
            class CustomConfig:
                def __init__(self, config_dict):
                    self.vocab_size = config_dict['vocab_size']
                    self.context_length = config_dict['max_seq_len']
                    self.emb_dim = config_dict['dim']
                    self.n_heads = getattr(model, 'n_heads', 8)
                    self.n_layers = getattr(model, 'n_layers', 6)

            config_obj = CustomConfig(config)

            # Load tokenizer for custom model
            tokenizer = self._load_custom_tokenizer() or self._load_default_tokenizer()
            if tokenizer is None:
                print("❌ Failed to load tokenizer for custom model")
                raise RuntimeError("No tokenizer available for custom model")

            return model, config_obj, tokenizer

        except Exception as e:
            print(f"❌ Failed to load custom model: {e}")
            raise

    def _load_standard_model(self, model_path: str, checkpoint: dict):
        """Load standard FeatureimanbugModel"""
        # Extract config
        if 'config' in checkpoint:
            config_dict = checkpoint['config']
            config = FeatureimanbugConfig(**config_dict)
        else:
            # Use default config if not found
            print("⚠️  No config found in checkpoint, using default")
            config = FeatureimanbugConfig()

        # Create model
        model = FeatureimanbugModel(config)

        # Load state dict
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)

        # Move to device
        model.to(self.device)
        model.eval()

        return model, config, None

        # Load tokenizer if not already loaded
        if tokenizer is None:
            print("🔄 Loading tokenizer...")
            custom_tokenizer = self._load_custom_tokenizer()
            if custom_tokenizer:
                tokenizer = custom_tokenizer
                print("✅ Custom tokenizer loaded")
            else:
                print("⚠️ Custom tokenizer failed, trying default...")
                default_tokenizer = self._load_default_tokenizer()
                if default_tokenizer:
                    tokenizer = default_tokenizer
                    print("✅ Default tokenizer loaded")

        # Ensure we have a tokenizer
        if tokenizer is None:
            print("❌ No tokenizer could be loaded!")
            raise RuntimeError("Failed to load any tokenizer")

        print(f"✅ Featureimanbug AI model loaded successfully")
        print(f"📊 Parameters: {model.get_num_params():,}")
        print(f"📚 Tokenizer vocab size: {getattr(tokenizer, 'vocab_size', 'Unknown')}")

        return model, config, tokenizer

    def _load_custom_tokenizer(self):
        """Try to load custom tokenizer from models/tokenizer.json"""
        import json

        tokenizer_path = "models/tokenizer.json"
        if os.path.exists(tokenizer_path):
            try:
                print(f"📚 Loading custom tokenizer from: {tokenizer_path}")

                # Create a simple tokenizer wrapper for your custom tokenizer
                class CustomTokenizerWrapper:
                    def __init__(self, tokenizer_data):
                        self.word_to_id = tokenizer_data['word_to_id']
                        self.id_to_word = {int(k): v for k, v in tokenizer_data['id_to_word'].items()}
                        self.vocab_size = tokenizer_data['vocab_size']
                        self.special_tokens = tokenizer_data['special_tokens']

                    def encode(self, text):
                        """Simple encoding - split by spaces and map to IDs"""
                        words = text.lower().split()
                        return [self.word_to_id.get(word, self.special_tokens['<UNK>']) for word in words]

                    def decode(self, token_ids):
                        """Simple decoding - map IDs back to words"""
                        words = []
                        for token_id in token_ids:
                            if isinstance(token_id, torch.Tensor):
                                token_id = token_id.item()
                            word = self.id_to_word.get(token_id, '<UNK>')
                            if word not in ['<PAD>', '< SOS >', '<EOS>']:
                                words.append(word)
                        return ' '.join(words)

                with open(tokenizer_path, 'r', encoding='utf-8') as f:
                    tokenizer_data = json.load(f)

                tokenizer = CustomTokenizerWrapper(tokenizer_data)
                print(f"✅ Custom tokenizer loaded with {tokenizer.vocab_size:,} tokens")
                return tokenizer

            except Exception as e:
                print(f"⚠️ Failed to load custom tokenizer: {e}")
                return None

        return None

    def _load_default_tokenizer(self):
        """Load default tiktoken tokenizer as fallback"""
        try:
            import tiktoken
            print("📚 Loading default GPT-2 tokenizer")
            return tiktoken.get_encoding("gpt2")
        except Exception as e:
            print(f"❌ Failed to load default tokenizer: {e}")
            return None
    
    def _test_generation(self):
        """Test model generation to ensure it's working."""
        try:
            if self.tokenizer is None:
                print("⚠️  Test generation skipped: No tokenizer available")
                return

            test_prompt = "Hello, I am"
            print(f"🧪 Testing generation with prompt: '{test_prompt}'")

            with torch.no_grad():
                # Handle different tokenizer types
                if hasattr(self.tokenizer, 'word_to_id'):
                    # Custom tokenizer
                    token_ids = self.tokenizer.encode(test_prompt)
                else:
                    # Tiktoken tokenizer
                    token_ids = self.tokenizer.encode(test_prompt, allowed_special={"<|endoftext|>"})

                input_ids = torch.tensor(
                    token_ids,
                    dtype=torch.long,
                    device=self.device
                ).unsqueeze(0)

                # Check if model has generate method
                if hasattr(self.model, 'generate'):
                    generated = self.model.generate(
                        input_ids,
                        max_new_tokens=10,
                        temperature=0.8
                    )
                else:
                    # Fallback for models without generate method
                    print("⚠️  Model doesn't have generate method, skipping test")
                    return

                # Decode the result
                if hasattr(self.tokenizer, 'word_to_id'):
                    # Custom tokenizer
                    generated_text = self.tokenizer.decode(generated[0].tolist())
                else:
                    # Tiktoken tokenizer
                    generated_text = self.tokenizer.decode(generated[0].tolist())

                print(f"✅ Test generation successful: '{generated_text}'")

        except Exception as e:
            print(f"⚠️  Test generation failed: {e}")
            # Don't let test generation failure stop the model loading
            pass
    
    def is_loaded(self) -> bool:
        """Check if model is loaded and ready."""
        return (self.model is not None and 
                self.tokenizer is not None and 
                self.generator is not None and 
                self.chat_interface is not None and
                not self.loading)
    
    def is_loading(self) -> bool:
        """Check if model is currently loading."""
        return self.loading
    
    def get_load_error(self) -> Optional[str]:
        """Get loading error if any."""
        return self.load_error
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information."""
        return self.model_info.copy()
    
    def get_generation_settings(self) -> Dict[str, Any]:
        """Get current generation settings."""
        return self.generation_settings.copy()
    
    def update_generation_settings(self, **kwargs):
        """Update generation settings."""
        self.generation_settings.update(kwargs)
        if self.chat_interface:
            self.chat_interface.update_generation_settings(**kwargs)
    
    def generate_text(self, prompt: str, **kwargs) -> str:
        """Generate text from prompt."""
        if not self.is_loaded():
            raise RuntimeError("Model not loaded")
        
        # Merge with default settings
        settings = self.generation_settings.copy()
        settings.update(kwargs)
        
        return self.generator.generate(prompt=prompt, **settings)
    
    def get_chat_interface(self) -> Optional[ChatInterface]:
        """Get chat interface instance."""
        return self.chat_interface
    
    def unload_model(self):
        """Unload model to free memory."""
        if self.model:
            del self.model
            self.model = None
        
        if self.tokenizer:
            del self.tokenizer
            self.tokenizer = None
        
        if self.generator:
            del self.generator
            self.generator = None
        
        if self.chat_interface:
            del self.chat_interface
            self.chat_interface = None
        
        # Clear CUDA cache if using GPU
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        self.model_info = {}
        print("🗑️  Model unloaded")
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive status information."""
        return {
            'loaded': self.is_loaded(),
            'loading': self.is_loading(),
            'error': self.get_load_error(),
            'model_info': self.get_model_info(),
            'generation_settings': self.get_generation_settings(),
            'device': str(self.device) if self.device else None,
            'memory_usage': self._get_memory_usage()
        }
    
    def _get_memory_usage(self) -> Dict[str, Any]:
        """Get memory usage information."""
        memory_info = {}
        
        if torch.cuda.is_available():
            memory_info['gpu'] = {
                'allocated': torch.cuda.memory_allocated() / 1024**3,  # GB
                'reserved': torch.cuda.memory_reserved() / 1024**3,    # GB
                'device_name': torch.cuda.get_device_name()
            }
        
        try:
            import psutil
            memory_info['cpu'] = {
                'used': psutil.virtual_memory().used / 1024**3,      # GB
                'total': psutil.virtual_memory().total / 1024**3,    # GB
                'percent': psutil.virtual_memory().percent
            }
        except ImportError:
            pass
        
        return memory_info
