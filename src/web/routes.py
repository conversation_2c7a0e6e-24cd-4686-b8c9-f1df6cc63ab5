"""
Featureimanbug AI - Web routes and API endpoints.
"""
from flask import render_template, request, jsonify, current_app
import os
import json
from datetime import datetime

# Global variables for model and chat interface
model_manager = None
chat_interface = None


def register_routes(app):
    """Register all routes with the Flask app."""
    
    @app.route('/')
    def index():
        """Main chat interface page."""
        return render_template('index.html')
    
    @app.route('/api/health')
    def health_check():
        """Health check endpoint."""
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'model_loaded': model_manager is not None,
            'version': '1.0.0'
        })
    
    @app.route('/api/model/status')
    def model_status():
        """Get model status and information."""
        if model_manager is None:
            return jsonify({
                'loaded': False,
                'error': 'Model not loaded'
            }), 503

        # Get basic model info
        model_info = model_manager.get_model_info()

        # Add custom tokenizer info if available
        if hasattr(model_manager.tokenizer, 'vocab_size'):
            model_info['tokenizer_type'] = 'custom'
            model_info['vocab_size'] = model_manager.tokenizer.vocab_size
            model_info['special_tokens'] = getattr(model_manager.tokenizer, 'special_tokens', {})
        else:
            model_info['tokenizer_type'] = 'tiktoken'

        # Add model file info
        model_info['model_file'] = 'featureimanbug_ai_model.pt'
        model_info['tokenizer_file'] = 'tokenizer.json' if os.path.exists('models/tokenizer.json') else 'default'

        return jsonify({
            'loaded': True,
            'model_info': model_info,
            'generation_settings': model_manager.get_generation_settings()
        })
    
    @app.route('/api/conversations', methods=['GET'])
    def get_conversations():
        """Get list of all conversations."""
        if chat_interface is None:
            return jsonify({'error': 'Chat interface not initialized'}), 503
        
        conversations = chat_interface.get_conversation_list()
        return jsonify({'conversations': conversations})
    
    @app.route('/api/conversations', methods=['POST'])
    def create_conversation():
        """Create a new conversation."""
        if chat_interface is None:
            return jsonify({'error': 'Chat interface not initialized'}), 503
        
        data = request.get_json()
        title = data.get('title', 'New Conversation')
        
        conv_id = chat_interface.start_new_conversation(title)
        return jsonify({
            'conversation_id': conv_id,
            'title': title,
            'created_at': datetime.now().isoformat()
        })
    
    @app.route('/api/conversations/<conversation_id>', methods=['GET'])
    def get_conversation(conversation_id):
        """Get a specific conversation."""
        if chat_interface is None:
            return jsonify({'error': 'Chat interface not initialized'}), 503
        
        conversation = chat_interface.conversation_manager.get_conversation(conversation_id)
        if not conversation:
            return jsonify({'error': 'Conversation not found'}), 404
        
        return jsonify({
            'conversation': conversation.to_dict()
        })
    
    @app.route('/api/conversations/<conversation_id>', methods=['DELETE'])
    def delete_conversation(conversation_id):
        """Delete a conversation."""
        if chat_interface is None:
            return jsonify({'error': 'Chat interface not initialized'}), 503
        
        success = chat_interface.delete_conversation(conversation_id)
        if not success:
            return jsonify({'error': 'Conversation not found'}), 404
        
        return jsonify({'success': True})
    
    @app.route('/api/conversations/<conversation_id>/messages', methods=['POST'])
    def send_message(conversation_id):
        """Send a message to a conversation (non-streaming)."""
        if chat_interface is None:
            return jsonify({'error': 'Chat interface not initialized'}), 503
        
        data = request.get_json()
        message = data.get('message', '').strip()
        
        if not message:
            return jsonify({'error': 'Message cannot be empty'}), 400
        
        try:
            # Set current conversation
            chat_interface.conversation_manager.set_current_conversation(conversation_id)
            
            # Generate response
            response = chat_interface.send_message(message, conversation_id)
            
            return jsonify({
                'response': response,
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/settings', methods=['GET'])
    def get_settings():
        """Get current generation settings."""
        if chat_interface is None:
            return jsonify({'error': 'Chat interface not initialized'}), 503
        
        return jsonify({
            'settings': chat_interface.generation_settings
        })
    
    @app.route('/api/settings', methods=['POST'])
    def update_settings():
        """Update generation settings."""
        if chat_interface is None:
            return jsonify({'error': 'Chat interface not initialized'}), 503
        
        data = request.get_json()
        
        # Validate settings
        valid_settings = {
            'max_new_tokens', 'strategy', 'temperature', 
            'top_k', 'top_p', 'repetition_penalty'
        }
        
        settings_update = {}
        for key, value in data.items():
            if key in valid_settings:
                settings_update[key] = value
        
        if settings_update:
            chat_interface.update_generation_settings(**settings_update)
            return jsonify({
                'success': True,
                'updated_settings': settings_update
            })
        else:
            return jsonify({'error': 'No valid settings provided'}), 400
    
    @app.route('/api/search', methods=['POST'])
    def search_conversations():
        """Search conversations."""
        if chat_interface is None:
            return jsonify({'error': 'Chat interface not initialized'}), 503
        
        data = request.get_json()
        query = data.get('query', '').strip()
        limit = data.get('limit', 10)
        
        if not query:
            return jsonify({'error': 'Search query cannot be empty'}), 400
        
        results = chat_interface.search_conversations(query, limit)
        return jsonify({'results': results})
    
    @app.route('/api/export', methods=['POST'])
    def export_conversations():
        """Export conversations."""
        if chat_interface is None:
            return jsonify({'error': 'Chat interface not initialized'}), 503
        
        data = request.get_json()
        format_type = data.get('format', 'json')
        
        try:
            # Create export filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"featureimanbug_ai_export_{timestamp}.{format_type}"
            filepath = os.path.join('exports', filename)
            
            # Ensure exports directory exists
            os.makedirs('exports', exist_ok=True)
            
            # Export conversations
            chat_interface.export_conversations(filepath, format_type)
            
            return jsonify({
                'success': True,
                'filename': filename,
                'filepath': filepath
            })
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/stats')
    def get_statistics():
        """Get chat statistics."""
        if chat_interface is None:
            return jsonify({'error': 'Chat interface not initialized'}), 503
        
        stats = chat_interface.get_statistics()
        return jsonify({'statistics': stats})


def set_model_manager(manager):
    """Set the global model manager."""
    global model_manager
    model_manager = manager


def set_chat_interface(interface):
    """Set the global chat interface."""
    global chat_interface
    chat_interface = interface
