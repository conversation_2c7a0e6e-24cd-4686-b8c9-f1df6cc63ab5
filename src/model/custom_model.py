"""
Custom model loader for models trained with MainTrainCode.py
This handles the specific architecture from your training script.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class CustomLanguageModel(nn.Module):
    """
    Custom Language Model - matches the architecture from MainTrainCode.py
    """
    def __init__(self, vocab_size, dim=384, n_layers=6, n_heads=6, max_seq_len=256):
        super().__init__()
        self.vocab_size = vocab_size
        self.dim = dim
        self.max_seq_len = max_seq_len
        self.n_heads = n_heads
        self.n_layers = n_layers
        
        # Embeddings
        self.tok_emb = nn.Embedding(vocab_size, dim)
        self.pos_emb = nn.Embedding(max_seq_len, dim)
        self.dropout = nn.Dropout(0.1)
        
        # Transformer blocks
        self.blocks = nn.ModuleList([
            TransformerBlock(dim, n_heads) for _ in range(n_layers)
        ])
        
        self.ln_f = nn.LayerNorm(dim)
        self.head = nn.Linear(dim, vocab_size)
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
    
    def get_num_params(self):
        """Get number of parameters."""
        return sum(p.numel() for p in self.parameters())
    
    def forward(self, x, targets=None):
        B, T = x.shape
        
        # Get embeddings
        tok_emb = self.tok_emb(x)
        pos_emb = self.pos_emb(torch.arange(T, device=x.device))
        x = self.dropout(tok_emb + pos_emb)
        
        # Apply transformer blocks
        for block in self.blocks:
            x = block(x)
        
        x = self.ln_f(x)
        logits = self.head(x)
        
        loss = None
        if targets is not None:
            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=0)
        
        return logits, loss
    
    def generate(self, input_ids, max_new_tokens=50, temperature=1.0, top_k=None, top_p=None):
        """Generate text from input_ids."""
        self.eval()
        
        with torch.no_grad():
            for _ in range(max_new_tokens):
                # Crop input_ids if too long
                if input_ids.size(1) >= self.max_seq_len:
                    input_ids = input_ids[:, -self.max_seq_len+1:]
                
                # Forward pass
                logits, _ = self(input_ids)
                logits = logits[:, -1, :] / temperature
                
                # Apply top_k filtering
                if top_k is not None:
                    values, indices = torch.topk(logits, top_k)
                    logits = torch.full_like(logits, float('-inf'))
                    logits.scatter_(1, indices, values)
                
                # Apply top_p filtering
                if top_p is not None:
                    sorted_logits, sorted_indices = torch.sort(logits, descending=True)
                    cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
                    
                    # Remove tokens with cumulative probability above the threshold
                    sorted_indices_to_remove = cumulative_probs > top_p
                    sorted_indices_to_remove[..., 1:] = sorted_indices_to_remove[..., :-1].clone()
                    sorted_indices_to_remove[..., 0] = 0
                    
                    indices_to_remove = sorted_indices_to_remove.scatter(1, sorted_indices, sorted_indices_to_remove)
                    logits[indices_to_remove] = float('-inf')
                
                # Sample from the distribution
                probs = F.softmax(logits, dim=-1)
                next_token = torch.multinomial(probs, num_samples=1)
                
                # Append to input_ids
                input_ids = torch.cat([input_ids, next_token], dim=1)
        
        return input_ids


class TransformerBlock(nn.Module):
    def __init__(self, dim, n_heads):
        super().__init__()
        self.attn = MultiHeadAttention(dim, n_heads)
        self.mlp = nn.Sequential(
            nn.Linear(dim, dim * 4),
            nn.GELU(),
            nn.Linear(dim * 4, dim),
            nn.Dropout(0.1)
        )
        self.ln1 = nn.LayerNorm(dim)
        self.ln2 = nn.LayerNorm(dim)
        self.dropout1 = nn.Dropout(0.1)
        self.dropout2 = nn.Dropout(0.1)
    
    def forward(self, x):
        x = x + self.dropout1(self.attn(self.ln1(x)))
        x = x + self.dropout2(self.mlp(self.ln2(x)))
        return x


class MultiHeadAttention(nn.Module):
    def __init__(self, dim, n_heads):
        super().__init__()
        assert dim % n_heads == 0
        
        self.n_heads = n_heads
        self.dim = dim
        self.head_dim = dim // n_heads
        
        self.qkv = nn.Linear(dim, dim * 3, bias=False)
        self.proj = nn.Linear(dim, dim)
        self.dropout = nn.Dropout(0.1)
    
    def forward(self, x):
        B, T, C = x.shape
        
        # Get Q, K, V
        qkv = self.qkv(x)
        q, k, v = qkv.chunk(3, dim=-1)
        
        # Reshape for multi-head attention
        q = q.view(B, T, self.n_heads, self.head_dim).transpose(1, 2)
        k = k.view(B, T, self.n_heads, self.head_dim).transpose(1, 2)
        v = v.view(B, T, self.n_heads, self.head_dim).transpose(1, 2)
        
        # Scaled dot-product attention
        att = (q @ k.transpose(-2, -1)) / (self.head_dim ** 0.5)
        
        # Causal mask
        mask = torch.triu(torch.ones(T, T, device=x.device), diagonal=1).bool()
        att.masked_fill_(mask, float('-inf'))
        
        att = F.softmax(att, dim=-1)
        att = self.dropout(att)
        
        # Apply attention to values
        out = att @ v
        out = out.transpose(1, 2).contiguous().view(B, T, C)
        
        return self.proj(out)


def load_custom_model(model_path, device='cpu'):
    """
    Load a custom model trained with MainTrainCode.py
    """
    print(f"📂 Loading custom model from: {model_path}")
    
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location=device)
    
    # Extract config
    if 'model_config' in checkpoint:
        config = checkpoint['model_config']
        vocab_size = config['vocab_size']
        dim = config['dim']
        max_seq_len = config['max_seq_len']
        
        # Infer n_heads and n_layers from state dict
        state_dict = checkpoint['model_state_dict']
        
        # Count layers by looking for block patterns
        n_layers = 0
        for key in state_dict.keys():
            if key.startswith('blocks.') and '.attn.qkv.weight' in key:
                layer_num = int(key.split('.')[1])
                n_layers = max(n_layers, layer_num + 1)
        
        # Infer n_heads from qkv weight shape
        qkv_weight = state_dict['blocks.0.attn.qkv.weight']
        qkv_dim = qkv_weight.shape[0]  # Should be dim * 3
        expected_dim = dim * 3
        
        if qkv_dim == expected_dim:
            # Standard multi-head attention
            # Try common head counts that divide evenly
            for n_heads in [8, 16, 12, 6, 4]:
                if dim % n_heads == 0:
                    break
            else:
                n_heads = 8  # Default fallback
        else:
            n_heads = 8  # Default fallback
        
        print(f"✅ Inferred architecture:")
        print(f"   • Vocab size: {vocab_size:,}")
        print(f"   • Dimension: {dim}")
        print(f"   • Layers: {n_layers}")
        print(f"   • Heads: {n_heads}")
        print(f"   • Max seq len: {max_seq_len}")
        
    else:
        raise ValueError("No model_config found in checkpoint")
    
    # Create model
    model = CustomLanguageModel(
        vocab_size=vocab_size,
        dim=dim,
        n_layers=n_layers,
        n_heads=n_heads,
        max_seq_len=max_seq_len
    )
    
    # Load state dict
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)
    model.eval()
    
    print(f"✅ Custom model loaded successfully!")
    print(f"📊 Parameters: {model.get_num_params():,}")
    
    return model, config
