#!/usr/bin/env python3
"""
Featureimanbug AI - Main application entry point.

This script provides a unified interface for training, inference, and chat functionality.
"""
import argparse
import sys
import os
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))


def train_model(args):
    """Train a new model or continue training."""
    import torch
    import tiktoken
    from src.config import GPTConfig, GPT_CONFIG_124M, GPT_CONFIG_355M, GPT_CONFIG_SMALL
    from src.model.gpt import GPTModel
    from src.training.trainer import GPTTrainer
    from src.training.dataset import create_simple_dataloader, load_text_data
    
    print("🚀 Starting Featureimanbug AI Training")
    print("=" * 50)
    
    # Load configuration
    if args.config:
        config = GPTConfig.load(args.config)
    else:
        # Use predefined config
        config_map = {
            'small': GPT_CONFIG_SMALL,
            '124m': GPT_CONFIG_124M,
            '355m': GPT_CONFIG_355M
        }
        config = config_map.get(args.model_size, GPT_CONFIG_SMALL)
    
    # Load training data
    if not os.path.exists(args.data_path):
        print(f"❌ Data file not found: {args.data_path}")
        return
    
    print(f"📖 Loading training data from: {args.data_path}")
    text_data = load_text_data(args.data_path)
    print(f"📊 Loaded {len(text_data)} characters")
    
    # Create model
    print(f"🧠 Creating {args.model_size} model...")
    model = GPTModel(config)
    
    # Create dataloader
    tokenizer = tiktoken.get_encoding("gpt2")
    dataloader = create_simple_dataloader(
        text=text_data,
        tokenizer=tokenizer,
        batch_size=config.batch_size,
        max_length=config.context_length,
        stride=config.context_length // 2
    )
    
    print(f"📦 Created dataloader with {len(dataloader)} batches")
    
    # Create trainer
    trainer = GPTTrainer(
        model=model,
        config=config,
        checkpoint_dir=args.checkpoint_dir,
        log_interval=args.log_interval,
        save_interval=args.save_interval
    )
    
    # Load checkpoint if resuming
    if args.resume and os.path.exists(args.resume):
        print(f"🔄 Resuming training from: {args.resume}")
        from src.training.utils import load_checkpoint
        load_checkpoint(args.resume, model, trainer.optimizer, trainer.scheduler)
    
    # Start training
    print(f"🎯 Training for {config.max_epochs} epochs...")
    try:
        for epoch in range(config.max_epochs):
            trainer.current_epoch = epoch
            epoch_metrics = trainer.train_epoch(dataloader, epoch)
            
            # Save epoch checkpoint
            trainer.save_checkpoint(epoch, epoch_metrics['avg_loss'])
            
        print("✅ Training completed successfully!")
        
    except KeyboardInterrupt:
        print("\n⏹️  Training interrupted by user")
        trainer.save_checkpoint(trainer.current_epoch, 0.0, filename="interrupted_checkpoint.pt")
        print("💾 Checkpoint saved")


def start_chat(args):
    """Start the chat interface."""
    import torch
    import tiktoken
    from src.config import GPTConfig
    from src.model.gpt import GPTModel
    from src.generation.generator import TextGenerator
    from src.chat.interface import ChatInterface
    from src.chat.cli import ChatCLI
    
    print("💬 Starting Featureimanbug AI Chat")
    print("=" * 50)
    
    # Load model and config
    if args.model_path and args.config_path:
        print(f"📂 Loading model from: {args.model_path}")
        print(f"⚙️  Loading config from: {args.config_path}")
        
        config = GPTConfig.load(args.config_path)
        model = GPTModel(config)
        
        # Load model weights
        checkpoint = torch.load(args.model_path, map_location='cpu')
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
            
        print("✅ Model loaded successfully")
    else:
        print("⚠️  No model specified, using default small model (untrained)")
        from src.config import GPT_CONFIG_SMALL
        config = GPT_CONFIG_SMALL
        model = GPTModel(config)
    
    # Set device and evaluation mode
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    model.eval()
    
    print(f"🖥️  Using device: {device}")
    
    # Initialize components
    tokenizer = tiktoken.get_encoding("gpt2")
    generator = TextGenerator(model, tokenizer)
    chat_interface = ChatInterface(model, generator, args.history_dir)
    
    # Start CLI
    cli = ChatCLI(chat_interface)
    cli.run()


def generate_text(args):
    """Generate text from a prompt."""
    import torch
    import tiktoken
    from src.config import GPTConfig
    from src.model.gpt import GPTModel
    from src.generation.generator import TextGenerator
    
    print("✨ Featureimanbug AI Text Generation")
    print("=" * 50)
    
    # Load model
    if args.model_path and args.config_path:
        config = GPTConfig.load(args.config_path)
        model = GPTModel(config)
        
        checkpoint = torch.load(args.model_path, map_location='cpu')
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
    else:
        print("⚠️  No model specified, using default small model (untrained)")
        from src.config import GPT_CONFIG_SMALL
        config = GPT_CONFIG_SMALL
        model = GPTModel(config)
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    model.eval()
    
    # Initialize generator
    tokenizer = tiktoken.get_encoding("gpt2")
    generator = TextGenerator(model, tokenizer)
    
    # Generate text
    print(f"📝 Prompt: {args.prompt}")
    print("🤖 Generated text:")
    print("-" * 30)
    
    generated_text = generator.generate(
        prompt=args.prompt,
        max_new_tokens=args.max_tokens,
        strategy=args.strategy,
        temperature=args.temperature,
        top_k=args.top_k,
        top_p=args.top_p
    )
    
    print(generated_text)
    print("-" * 30)


def start_web_interface(args):
    """Start the web-based chat interface."""
    import subprocess
    import sys

    # Build command
    cmd = [sys.executable, 'web_server.py']
    cmd.extend(['--host', args.host])
    cmd.extend(['--port', str(args.port)])

    if args.model_path:
        cmd.extend(['--model-path', args.model_path])
    if args.debug:
        cmd.append('--debug')
    if args.no_auto_load:
        cmd.append('--no-auto-load')

    # Run web server
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n👋 Web interface stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Web interface failed: {e}")


def start_custom_web_interface(args):
    """Start web interface with custom model detection."""
    print("🤖 Starting Featureimanbug AI with Custom Model Detection")
    print("=" * 60)

    # Use the custom startup script
    import subprocess

    try:
        cmd = [sys.executable, "start_custom_model.py"]
        if args.force_check:
            cmd.append("--force-check")

        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Custom web interface failed: {e}")
    except KeyboardInterrupt:
        print("\n👋 Shutting down...")


def convert_gpt2_model(args):
    """Convert GPT-2 model to PyTorch format."""
    import subprocess
    import sys

    # Build command
    cmd = [sys.executable, 'scripts/convert_gpt2.py']

    # Run conversion script
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Model conversion failed: {e}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Featureimanbug AI - GPT Language Model",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Train a small model
  python main.py train --data-path data.txt --model-size small

  # Start web interface (recommended)
  python main.py web

  # Start command-line chat
  python main.py chat --model-path model.pt --config-path config.json

  # Generate text
  python main.py generate --prompt "Hello world" --model-path model.pt --config-path config.json

  # Convert GPT-2 model
  python main.py convert
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Training command
    train_parser = subparsers.add_parser('train', help='Train a new model')
    train_parser.add_argument('--data-path', required=True, help='Path to training data file')
    train_parser.add_argument('--model-size', choices=['small', '124m', '355m'], default='small',
                             help='Model size configuration')
    train_parser.add_argument('--config', help='Path to custom config file')
    train_parser.add_argument('--checkpoint-dir', default='checkpoints', help='Checkpoint directory')
    train_parser.add_argument('--resume', help='Path to checkpoint to resume from')
    train_parser.add_argument('--log-interval', type=int, default=10, help='Logging interval')
    train_parser.add_argument('--save-interval', type=int, default=1000, help='Save interval')
    
    # Chat command
    chat_parser = subparsers.add_parser('chat', help='Start chat interface')
    chat_parser.add_argument('--model-path', help='Path to trained model')
    chat_parser.add_argument('--config-path', help='Path to model config')
    chat_parser.add_argument('--history-dir', default='chat_history', help='Chat history directory')
    
    # Generate command
    gen_parser = subparsers.add_parser('generate', help='Generate text from prompt')
    gen_parser.add_argument('--prompt', required=True, help='Text prompt')
    gen_parser.add_argument('--model-path', help='Path to trained model')
    gen_parser.add_argument('--config-path', help='Path to model config')
    gen_parser.add_argument('--max-tokens', type=int, default=100, help='Maximum tokens to generate')
    gen_parser.add_argument('--strategy', choices=['greedy', 'temperature', 'top_k', 'top_p'],
                           default='temperature', help='Generation strategy')
    gen_parser.add_argument('--temperature', type=float, default=0.8, help='Sampling temperature')
    gen_parser.add_argument('--top-k', type=int, default=50, help='Top-k sampling parameter')
    gen_parser.add_argument('--top-p', type=float, default=0.9, help='Top-p sampling parameter')

    # Web interface command
    web_parser = subparsers.add_parser('web', help='Start web-based chat interface')
    web_parser.add_argument('--host', default='127.0.0.1', help='Host to bind to')
    web_parser.add_argument('--port', type=int, default=5000, help='Port to bind to')
    web_parser.add_argument('--model-path', help='Path to model (auto-detected if not specified)')
    web_parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    web_parser.add_argument('--no-auto-load', action='store_true', help='Don\'t automatically load model')

    # Custom web interface command
    custom_web_parser = subparsers.add_parser('custom-web', help='Start web interface with custom model detection')
    custom_web_parser.add_argument('--host', default='127.0.0.1', help='Host to bind to')
    custom_web_parser.add_argument('--port', type=int, default=5000, help='Port to bind to')
    custom_web_parser.add_argument('--force-check', action='store_true', help='Force model file check')

    # Convert command
    convert_parser = subparsers.add_parser('convert', help='Convert GPT-2 model to PyTorch format')
    convert_parser.add_argument('--input-path', default='gpt2/355M', help='Path to GPT-2 model directory')
    convert_parser.add_argument('--output-path', default='models/gpt2_355m_converted.pt', help='Output path for converted model')

    args = parser.parse_args()

    if args.command == 'train':
        train_model(args)
    elif args.command == 'chat':
        start_chat(args)
    elif args.command == 'generate':
        generate_text(args)
    elif args.command == 'web':
        start_web_interface(args)
    elif args.command == 'custom-web':
        start_custom_web_interface(args)
    elif args.command == 'convert':
        convert_gpt2_model(args)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
